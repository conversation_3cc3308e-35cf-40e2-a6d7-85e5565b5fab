import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SessionManager {
  static const String _rememberMeKey = 'remember_me';
  static const String _userSessionKey = 'user_session';
  
  static SessionManager? _instance;
  static SessionManager get instance => _instance ??= SessionManager._();
  
  SessionManager._();
  
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Save the "Remember Me" preference
  Future<void> setRememberMe(bool rememberMe) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_rememberMeKey, rememberMe);
  }

  /// Get the "Remember Me" preference (defaults to true)
  Future<bool> getRememberMe() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_rememberMeKey) ?? true; // Default to true
  }

  /// Save user session data when "Remember Me" is enabled
  Future<void> saveSession() async {
    final rememberMe = await getRememberMe();
    if (!rememberMe) return;

    final session = _supabase.auth.currentSession;
    if (session != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userSessionKey, session.accessToken);
    }
  }

  /// Clear saved session data
  Future<void> clearSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userSessionKey);
  }

  /// Check if user should remain logged in based on "Remember Me" setting
  Future<bool> shouldRemainLoggedIn() async {
    final rememberMe = await getRememberMe();
    if (!rememberMe) {
      // If "Remember Me" is disabled, clear any saved session and sign out
      await clearSession();
      if (_supabase.auth.currentSession != null) {
        await _supabase.auth.signOut();
      }
      return false;
    }

    // If "Remember Me" is enabled, check if we have a valid session
    final session = _supabase.auth.currentSession;
    return session != null;
  }

  /// Initialize session on app startup
  Future<void> initializeSession() async {
    final rememberMe = await getRememberMe();
    
    if (!rememberMe) {
      // If "Remember Me" is disabled, ensure user is signed out
      await clearSession();
      if (_supabase.auth.currentSession != null) {
        await _supabase.auth.signOut();
      }
    }
    // If "Remember Me" is enabled, Supabase will automatically restore the session
    // if it's still valid, so we don't need to do anything special here
  }

  /// Handle sign out based on "Remember Me" setting
  Future<void> signOut() async {
    await clearSession();
    await _supabase.auth.signOut();
  }

  /// Handle successful sign in
  Future<void> onSignInSuccess() async {
    await saveSession();
  }
}
