enum ServiceType {
  washAndFold,
  dryClean,
  ironing,
}

enum PricingType {
  perPiece,
  perKg,
}

enum DeliveryType {
  pickupAndDelivery,
  inStorePickup,
  inStoreDelivery,
}

enum GarmentType {
  shirts,
  pants,
  dresses,
  suits,
  jackets,
  underwear,
  socks,
  bedding,
  towels,
  other,
}

class GarmentItem {
  final GarmentType type;
  final String name;
  final String description;
  double price;
  int quantity;
  final PricingType pricingType;
  final bool isCustom;
  double? weight; // For per-kg pricing

  GarmentItem({
    required this.type,
    required this.name,
    required this.description,
    required this.price,
    this.quantity = 0,
    this.pricingType = PricingType.perPiece,
    this.isCustom = false,
    this.weight,
  });

  double get totalPrice {
    if (pricingType == PricingType.perKg && weight != null) {
      return price * weight!;
    }
    return price * quantity;
  }

  String get priceDisplay {
    if (pricingType == PricingType.perKg) {
      return '\$${price.toStringAsFixed(2)}/kg';
    }
    return '\$${price.toStringAsFixed(2)}/piece';
  }
}

class Address {
  final String street;
  final String city;
  final String state;
  final String zipCode;
  final String? apartment;
  final String? instructions;

  Address({
    required this.street,
    required this.city,
    required this.state,
    required this.zipCode,
    this.apartment,
    this.instructions,
  });

  String get fullAddress {
    String address = street;
    if (apartment != null && apartment!.isNotEmpty) {
      address += ', $apartment';
    }
    address += ', $city, $state $zipCode';
    return address;
  }
}

class TimeSlot {
  final DateTime date;
  final String timeRange;
  final bool isAvailable;

  TimeSlot({
    required this.date,
    required this.timeRange,
    this.isAvailable = true,
  });

  String get formattedDate {
    final weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    return '${weekdays[date.weekday - 1]}, ${months[date.month - 1]} ${date.day}';
  }
}

class LaundryOrder {
  final List<ServiceType> serviceTypes;
  final List<GarmentItem> garments;
  Address? pickupAddress;
  Address? deliveryAddress;
  TimeSlot? pickupTimeSlot;
  TimeSlot? deliveryTimeSlot;
  String? specialInstructions;
  DeliveryType deliveryType;
  bool hasPackageDiscount;
  double packageDiscountPercent;

  LaundryOrder({
    required this.serviceTypes,
    this.garments = const [],
    this.pickupAddress,
    this.deliveryAddress,
    this.pickupTimeSlot,
    this.deliveryTimeSlot,
    this.specialInstructions,
    this.deliveryType = DeliveryType.pickupAndDelivery,
    this.hasPackageDiscount = false,
    this.packageDiscountPercent = 0.0,
  });

  double get subtotalPrice {
    return garments.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  double get discountAmount {
    if (hasPackageDiscount) {
      return subtotalPrice * (packageDiscountPercent / 100);
    }
    return 0.0;
  }

  double get totalPrice {
    return subtotalPrice - discountAmount;
  }

  int get totalItems {
    return garments.fold(0, (sum, item) => sum + item.quantity);
  }

  bool get isComplete {
    if (deliveryType == DeliveryType.pickupAndDelivery) {
      return garments.isNotEmpty &&
             pickupAddress != null &&
             deliveryAddress != null &&
             pickupTimeSlot != null &&
             deliveryTimeSlot != null;
    } else {
      return garments.isNotEmpty &&
             (deliveryType == DeliveryType.inStorePickup || pickupTimeSlot != null) &&
             (deliveryType == DeliveryType.inStoreDelivery || deliveryTimeSlot != null);
    }
  }

  String get serviceTypesDisplayName {
    return serviceTypes.map((type) {
      switch (type) {
        case ServiceType.washAndFold:
          return 'Wash & Fold';
        case ServiceType.dryClean:
          return 'Dry Clean';
        case ServiceType.ironing:
          return 'Ironing';
      }
    }).join(', ');
  }

  String get deliveryTypeDisplayName {
    switch (deliveryType) {
      case DeliveryType.pickupAndDelivery:
        return 'Pickup & Delivery';
      case DeliveryType.inStorePickup:
        return 'In-Store Pickup';
      case DeliveryType.inStoreDelivery:
        return 'In-Store Delivery';
    }
  }
}

// Helper class for garment definitions
class GarmentDefinitions {
  static List<GarmentItem> getAllGarments() {
    return [
      // Wash & Fold items
      GarmentItem(type: GarmentType.shirts, name: 'T-Shirts', description: 'Casual t-shirts, polo shirts', price: 3.50),
      GarmentItem(type: GarmentType.shirts, name: 'Casual Shirts', description: 'Button-up shirts, blouses', price: 4.00),
      GarmentItem(type: GarmentType.pants, name: 'Jeans', description: 'Denim jeans, casual pants', price: 4.50),
      GarmentItem(type: GarmentType.pants, name: 'Shorts', description: 'Casual shorts, athletic shorts', price: 3.00),
      GarmentItem(type: GarmentType.underwear, name: 'Underwear', description: 'Undergarments, bras, briefs', price: 1.50),
      GarmentItem(type: GarmentType.socks, name: 'Socks', description: 'All types of socks', price: 1.00),
      GarmentItem(type: GarmentType.bedding, name: 'Bed Sheets', description: 'Sheets, pillowcases', price: 6.00),
      GarmentItem(type: GarmentType.bedding, name: 'Comforters', description: 'Comforters, duvets', price: 12.00),
      GarmentItem(type: GarmentType.towels, name: 'Bath Towels', description: 'Large bath towels', price: 4.00),
      GarmentItem(type: GarmentType.towels, name: 'Hand Towels', description: 'Small hand towels', price: 2.50),

      // Dry Clean items
      GarmentItem(type: GarmentType.suits, name: 'Business Suits', description: 'Two-piece business suits', price: 18.00),
      GarmentItem(type: GarmentType.suits, name: 'Formal Suits', description: 'Tuxedos, formal wear', price: 25.00),
      GarmentItem(type: GarmentType.dresses, name: 'Cocktail Dresses', description: 'Semi-formal dresses', price: 15.00),
      GarmentItem(type: GarmentType.dresses, name: 'Evening Gowns', description: 'Formal evening wear', price: 22.00),
      GarmentItem(type: GarmentType.jackets, name: 'Blazers', description: 'Business blazers, sport coats', price: 12.00),
      GarmentItem(type: GarmentType.jackets, name: 'Winter Coats', description: 'Heavy coats, parkas', price: 20.00),
      GarmentItem(type: GarmentType.shirts, name: 'Dress Shirts', description: 'Formal dress shirts', price: 7.00),
      GarmentItem(type: GarmentType.pants, name: 'Dress Pants', description: 'Formal trousers, slacks', price: 9.00),

      // Ironing items
      GarmentItem(type: GarmentType.shirts, name: 'Shirts (Iron)', description: 'Professional pressing', price: 4.50),
      GarmentItem(type: GarmentType.pants, name: 'Pants (Iron)', description: 'Trouser pressing', price: 5.50),
      GarmentItem(type: GarmentType.dresses, name: 'Dresses (Iron)', description: 'Dress pressing', price: 7.00),
      GarmentItem(type: GarmentType.jackets, name: 'Jackets (Iron)', description: 'Light jacket pressing', price: 8.00),

      // Bulk pricing options
      GarmentItem(
        type: GarmentType.other,
        name: 'Mixed Load (Wash & Fold)',
        description: 'Mixed garments by weight',
        price: 3.50,
        pricingType: PricingType.perKg
      ),
      GarmentItem(
        type: GarmentType.other,
        name: 'Delicate Items (Wash)',
        description: 'Delicate items by weight',
        price: 4.50,
        pricingType: PricingType.perKg
      ),
    ];
  }

  static List<GarmentItem> getGarmentsForServices(List<ServiceType> serviceTypes) {
    final allGarments = getAllGarments();
    final filteredGarments = <GarmentItem>[];

    for (final serviceType in serviceTypes) {
      switch (serviceType) {
        case ServiceType.washAndFold:
          filteredGarments.addAll(allGarments.where((g) =>
            g.name.contains('T-Shirts') ||
            g.name.contains('Casual Shirts') ||
            g.name.contains('Jeans') ||
            g.name.contains('Shorts') ||
            g.name.contains('Underwear') ||
            g.name.contains('Socks') ||
            g.name.contains('Bed Sheets') ||
            g.name.contains('Comforters') ||
            g.name.contains('Towels') ||
            g.name.contains('Mixed Load') ||
            g.name.contains('Delicate Items')
          ));
          break;
        case ServiceType.dryClean:
          filteredGarments.addAll(allGarments.where((g) =>
            g.name.contains('Suits') ||
            g.name.contains('Dresses') ||
            g.name.contains('Blazers') ||
            g.name.contains('Winter Coats') ||
            g.name.contains('Dress Shirts') ||
            g.name.contains('Dress Pants')
          ));
          break;
        case ServiceType.ironing:
          filteredGarments.addAll(allGarments.where((g) =>
            g.name.contains('(Iron)')
          ));
          break;
      }
    }

    // Remove duplicates
    final uniqueGarments = <GarmentItem>[];
    final seenNames = <String>{};

    for (final garment in filteredGarments) {
      if (!seenNames.contains(garment.name)) {
        seenNames.add(garment.name);
        uniqueGarments.add(garment);
      }
    }

    return uniqueGarments;
  }

  static GarmentItem createCustomGarment({
    required String name,
    required String description,
    required double price,
    PricingType pricingType = PricingType.perPiece,
  }) {
    return GarmentItem(
      type: GarmentType.other,
      name: name,
      description: description,
      price: price,
      pricingType: pricingType,
      isCustom: true,
    );
  }
}
