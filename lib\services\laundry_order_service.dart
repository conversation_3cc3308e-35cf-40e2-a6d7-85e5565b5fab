import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/laundry_order.dart';

class LaundryOrderService {
  static final SupabaseClient _supabase = Supabase.instance.client;

  // Convert enum to string for database storage
  static String _serviceTypeToString(ServiceType type) {
    switch (type) {
      case ServiceType.washAndFold:
        return 'wash_and_fold';
      case ServiceType.dryClean:
        return 'dry_clean';
      case ServiceType.ironing:
        return 'ironing';
    }
  }

  static String _deliveryTypeToString(DeliveryType type) {
    switch (type) {
      case DeliveryType.pickupAndDelivery:
        return 'pickup_and_delivery';
      case DeliveryType.inStorePickup:
        return 'in_store_pickup';
      case DeliveryType.inStoreDelivery:
        return 'in_store_delivery';
    }
  }

  static String _garmentTypeToString(GarmentType type) {
    switch (type) {
      case GarmentType.shirts:
        return 'shirts';
      case GarmentType.pants:
        return 'pants';
      case GarmentType.dresses:
        return 'dresses';
      case GarmentType.suits:
        return 'suits';
      case GarmentType.jackets:
        return 'jackets';
      case GarmentType.underwear:
        return 'underwear';
      case GarmentType.socks:
        return 'socks';
      case GarmentType.bedding:
        return 'bedding';
      case GarmentType.towels:
        return 'towels';
      case GarmentType.other:
        return 'other';
    }
  }

  static String _pricingTypeToString(PricingType type) {
    switch (type) {
      case PricingType.perPiece:
        return 'per_piece';
      case PricingType.perKg:
        return 'per_kg';
    }
  }

  // Save address to database
  static Future<String> saveAddress(Address address) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase.from('addresses').insert({
        'user_id': user.id,
        'street': address.street,
        'apartment': address.apartment,
        'city': address.city,
        'state': address.state,
        'zip_code': address.zipCode,
        'instructions': address.instructions,
      }).select('id').single();

      return response['id'] as String;
    } catch (e) {
      throw Exception('Failed to save address: ${e.toString()}');
    }
  }

  // Save time slot to database (if it doesn't exist)
  static Future<String> saveTimeSlot(TimeSlot timeSlot) async {
    try {
      // First, try to find existing time slot
      final existingSlot = await _supabase
          .from('time_slots')
          .select('id')
          .eq('date', timeSlot.date.toIso8601String().split('T')[0])
          .eq('time_range', timeSlot.timeRange)
          .maybeSingle();

      if (existingSlot != null) {
        return existingSlot['id'] as String;
      }

      // Create new time slot if it doesn't exist
      final response = await _supabase.from('time_slots').insert({
        'date': timeSlot.date.toIso8601String().split('T')[0],
        'time_range': timeSlot.timeRange,
        'is_available': timeSlot.isAvailable,
      }).select('id').single();

      return response['id'] as String;
    } catch (e) {
      throw Exception('Failed to save time slot: ${e.toString()}');
    }
  }

  // Save complete laundry order
  static Future<String> saveLaundryOrder(LaundryOrder order) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Start a transaction-like operation
      String? pickupAddressId;
      String? deliveryAddressId;
      String? pickupTimeSlotId;
      String? deliveryTimeSlotId;

      // Save addresses if they exist
      if (order.pickupAddress != null) {
        pickupAddressId = await saveAddress(order.pickupAddress!);
      }

      if (order.deliveryAddress != null) {
        deliveryAddressId = await saveAddress(order.deliveryAddress!);
      }

      // Save time slots if they exist
      if (order.pickupTimeSlot != null) {
        pickupTimeSlotId = await saveTimeSlot(order.pickupTimeSlot!);
      }

      if (order.deliveryTimeSlot != null) {
        deliveryTimeSlotId = await saveTimeSlot(order.deliveryTimeSlot!);
      }

      // Convert service types to string array
      final serviceTypesStrings = order.serviceTypes
          .map((type) => _serviceTypeToString(type))
          .toList();

      // Generate custom order number
      final orderNumber = await generateOrderNumber();

      // Save the main order
      final orderResponse = await _supabase.from('laundry_orders').insert({
        'user_id': user.id,
        'order_number': orderNumber,
        'service_types': serviceTypesStrings,
        'delivery_type': _deliveryTypeToString(order.deliveryType),
        'pickup_address_id': pickupAddressId,
        'delivery_address_id': deliveryAddressId,
        'pickup_time_slot_id': pickupTimeSlotId,
        'delivery_time_slot_id': deliveryTimeSlotId,
        'subtotal_price': order.subtotalPrice,
        'discount_amount': order.discountAmount,
        'total_price': order.totalPrice,
        'has_package_discount': order.hasPackageDiscount,
        'package_discount_percent': order.packageDiscountPercent,
        'special_instructions': order.specialInstructions,
        'total_items': order.totalItems,
        'status': 'pending',
      }).select('id, order_number').single();

      final orderId = orderResponse['id'] as String;

      // Save garment items
      if (order.garments.isNotEmpty) {
        final garmentData = order.garments.map((garment) => {
          'order_id': orderId,
          'garment_type': _garmentTypeToString(garment.type),
          'name': garment.name,
          'description': garment.description,
          'price': garment.price,
          'quantity': garment.quantity,
          'weight': garment.weight,
          'pricing_type': _pricingTypeToString(garment.pricingType),
          'is_custom': garment.isCustom,
        }).toList();

        await _supabase.from('garment_items').insert(garmentData);
      }

      return orderResponse['order_number'] as String;
    } catch (e) {
      throw Exception('Failed to save laundry order: ${e.toString()}');
    }
  }

  // Get user's orders
  static Future<List<Map<String, dynamic>>> getUserOrders() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('laundry_orders')
          .select('''
            *,
            pickup_address:pickup_address_id(*),
            delivery_address:delivery_address_id(*),
            pickup_time_slot:pickup_time_slot_id(*),
            delivery_time_slot:delivery_time_slot_id(*),
            garment_items(*)
          ''')
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch orders: ${e.toString()}');
    }
  }

  // Get available time slots for a specific date
  static Future<List<Map<String, dynamic>>> getAvailableTimeSlots(DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0];
      
      final response = await _supabase
          .from('time_slots')
          .select('*')
          .eq('date', dateString)
          .eq('is_available', true)
          .lt('current_bookings', 'max_bookings')
          .order('time_range');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch time slots: ${e.toString()}');
    }
  }

  // Update order status
  static Future<void> updateOrderStatus(String orderId, String status) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _supabase
          .from('laundry_orders')
          .update({'status': status})
          .eq('id', orderId)
          .eq('user_id', user.id);
    } catch (e) {
      throw Exception('Failed to update order status: ${e.toString()}');
    }
  }

  // Cancel order
  static Future<void> cancelOrder(String orderId) async {
    try {
      await updateOrderStatus(orderId, 'cancelled');
    } catch (e) {
      throw Exception('Failed to cancel order: ${e.toString()}');
    }
  }

  // Get order by ID
  static Future<Map<String, dynamic>?> getOrderById(String orderId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('laundry_orders')
          .select('''
            *,
            pickup_address:pickup_address_id(*),
            delivery_address:delivery_address_id(*),
            pickup_time_slot:pickup_time_slot_id(*),
            delivery_time_slot:delivery_time_slot_id(*),
            garment_items(*)
          ''')
          .eq('id', orderId)
          .eq('user_id', user.id)
          .maybeSingle();

      return response;
    } catch (e) {
      throw Exception('Failed to fetch order: ${e.toString()}');
    }
  }

  // Generate next order number in format 1000001, 1000002, etc.
  static Future<String> generateOrderNumber() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get the highest order number for this user
      final response = await _supabase
          .from('laundry_orders')
          .select('order_number')
          .eq('user_id', user.id)
          .like('order_number', '10%') // Only get our format numbers
          .order('order_number', ascending: false)
          .limit(1)
          .maybeSingle();

      int nextNumber = 1000001; // Starting number

      if (response != null) {
        final lastOrderNumber = response['order_number'] as String;
        // Extract number and increment
        final lastNumber = int.tryParse(lastOrderNumber) ?? 1000000;
        nextNumber = lastNumber + 1;
      }

      return nextNumber.toString();
    } catch (e) {
      throw Exception('Failed to generate order number: ${e.toString()}');
    }
  }

  // Get all orders for user with pagination
  static Future<List<Map<String, dynamic>>> getAllUserOrders({
    int limit = 20,
    int offset = 0,
    String? statusFilter,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      var query = _supabase
          .from('laundry_orders')
          .select('''
            *,
            pickup_address:pickup_address_id(*),
            delivery_address:delivery_address_id(*),
            pickup_time_slot:pickup_time_slot_id(*),
            delivery_time_slot:delivery_time_slot_id(*),
            garment_items(*)
          ''')
          .eq('user_id', user.id);

      if (statusFilter != null && statusFilter.isNotEmpty) {
        query = query.eq('status', statusFilter);
      }

      query = query
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch orders: ${e.toString()}');
    }
  }

  // Delete order (only if pending)
  static Future<void> deleteOrder(String orderId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // First check if order is pending
      final order = await _supabase
          .from('laundry_orders')
          .select('status')
          .eq('id', orderId)
          .eq('user_id', user.id)
          .single();

      if (order['status'] != 'pending') {
        throw Exception('Only pending orders can be deleted');
      }

      await _supabase
          .from('laundry_orders')
          .delete()
          .eq('id', orderId)
          .eq('user_id', user.id);
    } catch (e) {
      throw Exception('Failed to delete order: ${e.toString()}');
    }
  }
}
