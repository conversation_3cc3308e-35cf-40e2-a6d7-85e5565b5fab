import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../screens/profile_screen.dart';
import '../screens/my_laundry_screen.dart';
import '../screens/service_management_screen.dart';
import '../screens/settings_screen.dart';
import '../utils/auth_service.dart';
import '../utils/snackbar_helper.dart';

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  final _authService = AuthService();
  bool _isSigningOut = false;

  Future<void> _signOut() async {
    setState(() => _isSigningOut = true);

    try {
      await _authService.signOut();
      if (mounted) {
        Navigator.pop(context); // Close drawer
        SnackbarHelper.showSuccess(context, 'Signed out successfully');
      }
    } catch (error) {
      if (mounted) {
        SnackbarHelper.showError(context, 'Error signing out: $error');
      }
    } finally {
      if (mounted) {
        setState(() => _isSigningOut = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = Supabase.instance.client.auth.currentUser;
    
    return Drawer(
      child: Column(
        children: [
          // Drawer Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(16, 60, 16, 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                  child: Icon(
                    Icons.local_laundry_service_rounded,
                    size: 30,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  user?.userMetadata?['full_name'] ?? 'User',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  user?.email ?? '',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    user?.emailConfirmedAt != null ? 'Verified' : 'Unverified',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Menu Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.home_outlined,
                  title: 'Home',
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
                
                _buildDrawerItem(
                  icon: Icons.person_outline,
                  title: 'Profile',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const ProfileScreen()),
                    );
                  },
                ),
                
                _buildDrawerItem(
                  icon: Icons.local_laundry_service_outlined,
                  title: 'My Laundry',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const MyLaundryScreen()),
                    );
                  },
                ),

                _buildDrawerItem(
                  icon: Icons.settings_applications,
                  title: 'Service Management',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const ServiceManagementScreen()),
                    );
                  },
                ),

                _buildDrawerItem(
                  icon: Icons.history,
                  title: 'Order History',
                  onTap: () {
                    Navigator.pop(context);
                    SnackbarHelper.showInfo(context, 'Feature coming soon!');
                  },
                ),
                
                _buildDrawerItem(
                  icon: Icons.location_on_outlined,
                  title: 'Addresses',
                  onTap: () {
                    Navigator.pop(context);
                    SnackbarHelper.showInfo(context, 'Feature coming soon!');
                  },
                ),
                
                _buildDrawerItem(
                  icon: Icons.payment_outlined,
                  title: 'Payment Methods',
                  onTap: () {
                    Navigator.pop(context);
                    SnackbarHelper.showInfo(context, 'Feature coming soon!');
                  },
                ),
                
                const Divider(height: 32),
                
                _buildDrawerItem(
                  icon: Icons.settings_outlined,
                  title: 'Settings',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const SettingsScreen()),
                    );
                  },
                ),
                
                _buildDrawerItem(
                  icon: Icons.help_outline,
                  title: 'Help & Support',
                  onTap: () {
                    Navigator.pop(context);
                    SnackbarHelper.showInfo(context, 'Feature coming soon!');
                  },
                ),
                
                _buildDrawerItem(
                  icon: Icons.info_outline,
                  title: 'About',
                  onTap: () {
                    Navigator.pop(context);
                    _showAboutDialog();
                  },
                ),
              ],
            ),
          ),
          
          // Logout Button at Bottom
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: _buildDrawerItem(
              icon: Icons.logout,
              title: 'Sign Out',
              isLoading: _isSigningOut,
              onTap: _isSigningOut ? null : _signOut,
              textColor: Colors.red[600],
              iconColor: Colors.red[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback? onTap,
    Color? textColor,
    Color? iconColor,
    bool isLoading = false,
  }) {
    return ListTile(
      leading: isLoading
          ? SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  iconColor ?? Theme.of(context).colorScheme.onSurface,
                ),
              ),
            )
          : Icon(
              icon,
              color: iconColor ?? Theme.of(context).colorScheme.onSurface,
            ),
      title: Text(
        title,
        style: TextStyle(
          color: textColor ?? Theme.of(context).colorScheme.onSurface,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      enabled: onTap != null,
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'Drop & Go',
      applicationVersion: '1.0.0',
      applicationIcon: Icon(
        Icons.local_laundry_service_rounded,
        size: 48,
        color: Theme.of(context).colorScheme.primary,
      ),
      children: [
        const Text(
          'Your laundry management solution. Drop off your laundry and go about your day while we take care of the rest.',
        ),
      ],
    );
  }
}
