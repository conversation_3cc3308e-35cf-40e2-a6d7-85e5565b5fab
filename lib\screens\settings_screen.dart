import 'package:flutter/material.dart';
import '../models/store_details.dart';
import '../services/store_details_service.dart';
import '../utils/snackbar_helper.dart';
import '../widgets/custom_text_field.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  StoreDetails? _storeDetails;
  bool _isLoading = true;
  bool _isSaving = false;

  // Form controllers
  final _storeNameController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipCodeController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _websiteController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _minimumOrderController = TextEditingController();
  final _deliveryFeeController = TextEditingController();
  final _freeDeliveryThresholdController = TextEditingController();
  final _taxRateController = TextEditingController();
  final _pickupRadiusController = TextEditingController();
  final _specialInstructionsController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadStoreDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _storeNameController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipCodeController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _descriptionController.dispose();
    _minimumOrderController.dispose();
    _deliveryFeeController.dispose();
    _freeDeliveryThresholdController.dispose();
    _taxRateController.dispose();
    _pickupRadiusController.dispose();
    _specialInstructionsController.dispose();
    super.dispose();
  }

  Future<void> _loadStoreDetails() async {
    try {
      setState(() => _isLoading = true);
      final storeDetails = await StoreDetailsService.getOrCreateStoreDetails();
      setState(() {
        _storeDetails = storeDetails;
        _populateControllers();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        SnackbarHelper.showError(context, 'Failed to load store details: $e');
      }
    }
  }

  void _populateControllers() {
    if (_storeDetails == null) return;

    _storeNameController.text = _storeDetails!.storeName;
    _addressController.text = _storeDetails!.address;
    _cityController.text = _storeDetails!.city;
    _stateController.text = _storeDetails!.state;
    _zipCodeController.text = _storeDetails!.zipCode;
    _phoneController.text = _storeDetails!.phoneNumber;
    _emailController.text = _storeDetails!.email ?? '';
    _websiteController.text = _storeDetails!.website ?? '';
    _descriptionController.text = _storeDetails!.description;
    _minimumOrderController.text = _storeDetails!.minimumOrderAmount.toString();
    _deliveryFeeController.text = _storeDetails!.deliveryFee.toString();
    _freeDeliveryThresholdController.text = _storeDetails!.freeDeliveryThreshold.toString();
    _taxRateController.text = (_storeDetails!.taxRate * 100).toStringAsFixed(2);
    _pickupRadiusController.text = _storeDetails!.pickupRadius.toString();
    _specialInstructionsController.text = _storeDetails!.specialInstructions ?? '';
  }

  Future<void> _saveStoreDetails() async {
    if (_storeDetails == null) return;

    try {
      setState(() => _isSaving = true);

      final updatedDetails = _storeDetails!.copyWith(
        storeName: _storeNameController.text.trim(),
        address: _addressController.text.trim(),
        city: _cityController.text.trim(),
        state: _stateController.text.trim(),
        zipCode: _zipCodeController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        website: _websiteController.text.trim().isEmpty ? null : _websiteController.text.trim(),
        description: _descriptionController.text.trim(),
        minimumOrderAmount: double.tryParse(_minimumOrderController.text) ?? 0.0,
        deliveryFee: double.tryParse(_deliveryFeeController.text) ?? 0.0,
        freeDeliveryThreshold: double.tryParse(_freeDeliveryThresholdController.text) ?? 0.0,
        taxRate: (double.tryParse(_taxRateController.text) ?? 0.0) / 100,
        pickupRadius: double.tryParse(_pickupRadiusController.text) ?? 0.0,
        specialInstructions: _specialInstructionsController.text.trim().isEmpty 
            ? null 
            : _specialInstructionsController.text.trim(),
      );

      // Validate the data
      final validationError = StoreDetailsService.validateStoreDetails(updatedDetails);
      if (validationError != null) {
        SnackbarHelper.showError(context, validationError);
        return;
      }

      final savedDetails = await StoreDetailsService.updateStoreDetails(updatedDetails);
      setState(() {
        _storeDetails = savedDetails;
        _isSaving = false;
      });

      if (mounted) {
        SnackbarHelper.showSuccess(context, 'Store details updated successfully!');
      }
    } catch (e) {
      setState(() => _isSaving = false);
      if (mounted) {
        SnackbarHelper.showError(context, 'Failed to save store details: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.store), text: 'Basic Info'),
            Tab(icon: Icon(Icons.attach_money), text: 'Pricing'),
            Tab(icon: Icon(Icons.schedule), text: 'Hours'),
            Tab(icon: Icon(Icons.notifications), text: 'Notifications'),
          ],
        ),
        actions: [
          if (!_isLoading)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: _isSaving
                  ? const Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : TextButton(
                      onPressed: _saveStoreDetails,
                      child: const Text('Save'),
                    ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildBasicInfoTab(),
                _buildPricingTab(),
                _buildBusinessHoursTab(),
                _buildNotificationsTab(),
              ],
            ),
    );
  }

  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Company Information',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _storeNameController,
            label: 'Store Name *',
            hintText: 'Enter your store name',
            prefixIcon: Icons.store,
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _descriptionController,
            label: 'Description',
            hintText: 'Brief description of your services',
            prefixIcon: Icons.description,
            maxLines: 3,
          ),
          const SizedBox(height: 24),

          Text(
            'Contact Information',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _addressController,
            label: 'Address *',
            hintText: 'Street address',
            prefixIcon: Icons.location_on,
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                flex: 2,
                child: CustomTextField(
                  controller: _cityController,
                  label: 'City *',
                  hintText: 'City',
                  prefixIcon: Icons.location_city,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  controller: _stateController,
                  label: 'State *',
                  hintText: 'State',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  controller: _zipCodeController,
                  label: 'ZIP *',
                  hintText: 'ZIP Code',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _phoneController,
            label: 'Phone Number *',
            hintText: '(*************',
            prefixIcon: Icons.phone,
            keyboardType: TextInputType.phone,
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _emailController,
            label: 'Email',
            hintText: '<EMAIL>',
            prefixIcon: Icons.email,
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _websiteController,
            label: 'Website',
            hintText: 'https://yourstore.com',
            prefixIcon: Icons.web,
            keyboardType: TextInputType.url,
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _specialInstructionsController,
            label: 'Special Instructions',
            hintText: 'Any special instructions for customers',
            prefixIcon: Icons.info,
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildPricingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pricing Configuration',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _minimumOrderController,
            label: 'Minimum Order Amount',
            hintText: '20.00',
            prefixIcon: Icons.attach_money,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _deliveryFeeController,
            label: 'Delivery Fee',
            hintText: '5.00',
            prefixIcon: Icons.local_shipping,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _freeDeliveryThresholdController,
            label: 'Free Delivery Threshold',
            hintText: '50.00',
            prefixIcon: Icons.free_breakfast,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _taxRateController,
            label: 'Tax Rate (%)',
            hintText: '8.75',
            prefixIcon: Icons.percent,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _pickupRadiusController,
            label: 'Pickup Radius (miles)',
            hintText: '10.0',
            prefixIcon: Icons.location_searching,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
          const SizedBox(height: 24),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue[700]),
                    const SizedBox(width: 8),
                    Text(
                      'Pricing Information',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '• Minimum Order Amount: The smallest order value you accept\n'
                  '• Delivery Fee: Standard fee for delivery service\n'
                  '• Free Delivery Threshold: Order amount for free delivery\n'
                  '• Tax Rate: Enter as percentage (e.g., 8.75 for 8.75%)\n'
                  '• Pickup Radius: Maximum distance for pickup/delivery',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessHoursTab() {
    if (_storeDetails == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Business Hours',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          ...['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
              .map((day) => _buildDayHours(day)),

          const SizedBox(height: 24),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.schedule, color: Colors.green[700]),
                    const SizedBox(width: 8),
                    Text(
                      'Hours Information',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Set your business hours to let customers know when you\'re available for pickup and delivery services.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayHours(String day) {
    final dayData = _storeDetails!.businessHours[day] as Map<String, dynamic>? ?? {};
    final isClosed = dayData['closed'] as bool? ?? false;
    final openTime = dayData['open'] as String? ?? '08:00';
    final closeTime = dayData['close'] as String? ?? '20:00';

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    day.substring(0, 1).toUpperCase() + day.substring(1),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Switch(
                  value: !isClosed,
                  onChanged: (value) {
                    setState(() {
                      final updatedHours = Map<String, dynamic>.from(_storeDetails!.businessHours);
                      updatedHours[day] = {
                        'open': openTime,
                        'close': closeTime,
                        'closed': !value,
                      };
                      _storeDetails = _storeDetails!.copyWith(businessHours: updatedHours);
                    });
                  },
                ),
              ],
            ),
            if (!isClosed) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectTime(day, 'open', openTime),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.access_time, size: 20),
                            const SizedBox(width: 8),
                            Text('Open: $openTime'),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectTime(day, 'close', closeTime),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.access_time, size: 20),
                            const SizedBox(width: 8),
                            Text('Close: $closeTime'),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _selectTime(String day, String timeType, String currentTime) async {
    final timeParts = currentTime.split(':');
    final initialTime = TimeOfDay(
      hour: int.parse(timeParts[0]),
      minute: int.parse(timeParts[1]),
    );

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (selectedTime != null) {
      final formattedTime = '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';

      setState(() {
        final updatedHours = Map<String, dynamic>.from(_storeDetails!.businessHours);
        final dayData = Map<String, dynamic>.from(updatedHours[day] as Map<String, dynamic>);
        dayData[timeType] = formattedTime;
        updatedHours[day] = dayData;
        _storeDetails = _storeDetails!.copyWith(businessHours: updatedHours);
      });
    }
  }

  Widget _buildNotificationsTab() {
    if (_storeDetails == null) return const SizedBox();

    final notifications = _storeDetails!.notificationSettings;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notification Settings',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          _buildNotificationTile(
            'Email Notifications',
            'Receive general notifications via email',
            Icons.email,
            notifications['email_notifications'] as bool? ?? true,
            'email_notifications',
          ),

          _buildNotificationTile(
            'SMS Notifications',
            'Receive notifications via text message',
            Icons.sms,
            notifications['sms_notifications'] as bool? ?? false,
            'sms_notifications',
          ),

          _buildNotificationTile(
            'Order Confirmations',
            'Get notified when orders are confirmed',
            Icons.check_circle,
            notifications['order_confirmations'] as bool? ?? true,
            'order_confirmations',
          ),

          _buildNotificationTile(
            'Pickup Reminders',
            'Reminders for scheduled pickups',
            Icons.schedule,
            notifications['pickup_reminders'] as bool? ?? true,
            'pickup_reminders',
          ),

          _buildNotificationTile(
            'Delivery Notifications',
            'Updates on delivery status',
            Icons.local_shipping,
            notifications['delivery_notifications'] as bool? ?? true,
            'delivery_notifications',
          ),

          _buildNotificationTile(
            'Promotional Emails',
            'Receive promotional offers and updates',
            Icons.local_offer,
            notifications['promotional_emails'] as bool? ?? false,
            'promotional_emails',
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationTile(String title, String subtitle, IconData icon, bool value, String key) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        secondary: Icon(icon),
        value: value,
        onChanged: (newValue) {
          setState(() {
            final updatedNotifications = Map<String, dynamic>.from(_storeDetails!.notificationSettings);
            updatedNotifications[key] = newValue;
            _storeDetails = _storeDetails!.copyWith(notificationSettings: updatedNotifications);
          });
        },
      ),
    );
  }
}
