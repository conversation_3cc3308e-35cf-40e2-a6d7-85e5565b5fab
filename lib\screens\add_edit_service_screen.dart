import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/custom_service_management_service.dart';
import '../models/laundry_order.dart';
import '../utils/snackbar_helper.dart';

class AddEditServiceScreen extends StatefulWidget {
  final String? serviceId; // null for add, non-null for edit

  const AddEditServiceScreen({super.key, this.serviceId});

  @override
  State<AddEditServiceScreen> createState() => _AddEditServiceScreenState();
}

class _AddEditServiceScreenState extends State<AddEditServiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _basePriceController = TextEditingController();
  final _featureController = TextEditingController();

  ServiceType _selectedServiceType = ServiceType.washAndFold;
  PricingType _selectedPricingType = PricingType.perPiece;
  List<String> _features = [];
  String _selectedColor = '#2196F3';
  bool _isLoading = false;
  bool _isLoadingData = false;

  final List<Color> _availableColors = [
    const Color(0xFF2196F3), // Blue
    const Color(0xFF4CAF50), // Green
    const Color(0xFFFF9800), // Orange
    const Color(0xFFE91E63), // Pink
    const Color(0xFF9C27B0), // Purple
    const Color(0xFF673AB7), // Deep Purple
    const Color(0xFF3F51B5), // Indigo
    const Color(0xFF009688), // Teal
    const Color(0xFF795548), // Brown
    const Color(0xFF607D8B), // Blue Grey
  ];

  @override
  void initState() {
    super.initState();
    if (widget.serviceId != null) {
      _loadServiceData();
    }
  }

  Future<void> _loadServiceData() async {
    setState(() {
      _isLoadingData = true;
    });

    try {
      final service = await CustomServiceManagementService.getCustomServiceById(widget.serviceId!);
      if (service != null && mounted) {
        setState(() {
          _titleController.text = service['title'] as String;
          _descriptionController.text = service['description'] as String? ?? '';
          _basePriceController.text = (service['base_price'] as num).toString();
          _selectedServiceType = _stringToServiceType(service['service_type'] as String);
          _selectedPricingType = _stringToPricingType(service['pricing_type'] as String);
          _features = List<String>.from(service['features'] as List? ?? []);
          _selectedColor = service['color_hex'] as String? ?? '#2196F3';
        });
      }
    } catch (e) {
      if (mounted) {
        SnackbarHelper.showError(context, 'Failed to load service data: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingData = false;
        });
      }
    }
  }

  ServiceType _stringToServiceType(String str) {
    switch (str) {
      case 'wash_and_fold':
        return ServiceType.washAndFold;
      case 'dry_clean':
        return ServiceType.dryClean;
      case 'ironing':
        return ServiceType.ironing;
      default:
        return ServiceType.washAndFold;
    }
  }

  PricingType _stringToPricingType(String str) {
    switch (str) {
      case 'per_piece':
        return PricingType.perPiece;
      case 'per_kg':
        return PricingType.perKg;
      default:
        return PricingType.perPiece;
    }
  }

  void _addFeature() {
    if (_featureController.text.trim().isNotEmpty) {
      setState(() {
        _features.add(_featureController.text.trim());
        _featureController.clear();
      });
    }
  }

  void _removeFeature(int index) {
    setState(() {
      _features.removeAt(index);
    });
  }

  Future<void> _saveService() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final basePrice = double.parse(_basePriceController.text);

      if (widget.serviceId == null) {
        // Create new service
        await CustomServiceManagementService.createCustomService(
          serviceType: _selectedServiceType,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          basePrice: basePrice,
          pricingType: _selectedPricingType,
          features: _features,
          colorHex: _selectedColor,
        );
        SnackbarHelper.showSuccess(context, 'Service created successfully');
      } else {
        // Update existing service
        await CustomServiceManagementService.updateCustomService(
          serviceId: widget.serviceId!,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          basePrice: basePrice,
          pricingType: _selectedPricingType,
          features: _features,
          colorHex: _selectedColor,
        );
        SnackbarHelper.showSuccess(context, 'Service updated successfully');
      }

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      SnackbarHelper.showError(context, 'Failed to save service: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.serviceId == null ? 'Add Service' : 'Edit Service'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (!_isLoading && !_isLoadingData)
            TextButton(
              onPressed: _saveService,
              child: const Text('Save'),
            ),
        ],
      ),
      body: _isLoadingData
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Service Type Selection
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Service Type',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            DropdownButtonFormField<ServiceType>(
                              value: _selectedServiceType,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              ),
                              items: ServiceType.values.map((type) {
                                return DropdownMenuItem(
                                  value: type,
                                  child: Text(_getServiceTypeDisplayName(type)),
                                );
                              }).toList(),
                              onChanged: widget.serviceId == null ? (value) {
                                if (value != null) {
                                  setState(() {
                                    _selectedServiceType = value;
                                  });
                                }
                              } : null, // Disable editing service type for existing services
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Basic Information
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Basic Information',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            TextFormField(
                              controller: _titleController,
                              decoration: const InputDecoration(
                                labelText: 'Service Title',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Please enter a service title';
                                }
                                return null;
                              },
                            ),
                            
                            const SizedBox(height: 16),
                            
                            TextFormField(
                              controller: _descriptionController,
                              decoration: const InputDecoration(
                                labelText: 'Description',
                                border: OutlineInputBorder(),
                              ),
                              maxLines: 3,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Pricing Information
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Pricing',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: _basePriceController,
                                    decoration: const InputDecoration(
                                      labelText: 'Base Price',
                                      border: OutlineInputBorder(),
                                      prefixText: '\$',
                                    ),
                                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                                    ],
                                    validator: (value) {
                                      if (value == null || value.trim().isEmpty) {
                                        return 'Please enter a price';
                                      }
                                      final price = double.tryParse(value);
                                      if (price == null || price <= 0) {
                                        return 'Please enter a valid price';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: DropdownButtonFormField<PricingType>(
                                    value: _selectedPricingType,
                                    decoration: const InputDecoration(
                                      labelText: 'Pricing Type',
                                      border: OutlineInputBorder(),
                                    ),
                                    items: PricingType.values.map((type) {
                                      return DropdownMenuItem(
                                        value: type,
                                        child: Text(type == PricingType.perPiece ? 'Per Piece' : 'Per Kg'),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _selectedPricingType = value;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Color Selection
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Color Theme',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: _availableColors.map((color) {
                                final colorHex = '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
                                final isSelected = _selectedColor == colorHex;
                                
                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _selectedColor = colorHex;
                                    });
                                  },
                                  child: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: color,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: isSelected ? Colors.black : Colors.grey[300]!,
                                        width: isSelected ? 3 : 1,
                                      ),
                                    ),
                                    child: isSelected
                                        ? const Icon(
                                            Icons.check,
                                            color: Colors.white,
                                            size: 20,
                                          )
                                        : null,
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Features
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Features',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: _featureController,
                                    decoration: const InputDecoration(
                                      labelText: 'Add Feature',
                                      border: OutlineInputBorder(),
                                    ),
                                    onFieldSubmitted: (_) => _addFeature(),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                IconButton(
                                  onPressed: _addFeature,
                                  icon: const Icon(Icons.add),
                                ),
                              ],
                            ),
                            
                            if (_features.isNotEmpty) ...[
                              const SizedBox(height: 16),
                              Wrap(
                                spacing: 8,
                                runSpacing: 8,
                                children: _features.asMap().entries.map((entry) {
                                  final index = entry.key;
                                  final feature = entry.value;
                                  
                                  return Chip(
                                    label: Text(feature),
                                    deleteIcon: const Icon(Icons.close, size: 18),
                                    onDeleted: () => _removeFeature(index),
                                  );
                                }).toList(),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Save Button
                    ElevatedButton(
                      onPressed: _isLoading ? null : _saveService,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Text(widget.serviceId == null ? 'Create Service' : 'Update Service'),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  String _getServiceTypeDisplayName(ServiceType type) {
    switch (type) {
      case ServiceType.washAndFold:
        return 'Wash & Fold';
      case ServiceType.dryClean:
        return 'Dry Clean';
      case ServiceType.ironing:
        return 'Ironing';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _basePriceController.dispose();
    _featureController.dispose();
    super.dispose();
  }
}
