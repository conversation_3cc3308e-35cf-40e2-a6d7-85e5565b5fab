import 'package:flutter/material.dart';
import '../models/laundry_order.dart';
import 'address_selection_screen.dart';
import 'time_slot_selection_screen.dart';

class DeliveryTypeSelectionScreen extends StatefulWidget {
  final LaundryOrder order;

  const DeliveryTypeSelectionScreen({
    super.key,
    required this.order,
  });

  @override
  State<DeliveryTypeSelectionScreen> createState() => _DeliveryTypeSelectionScreenState();
}

class _DeliveryTypeSelectionScreenState extends State<DeliveryTypeSelectionScreen> {
  DeliveryType _selectedDeliveryType = DeliveryType.pickupAndDelivery;

  final List<DeliveryOption> _deliveryOptions = [
    DeliveryOption(
      type: DeliveryType.pickupAndDelivery,
      title: 'Pickup & Delivery',
      description: 'We pick up from your location and deliver back to you',
      icon: Icons.local_shipping,
      features: [
        'Convenient door-to-door service',
        'No need to visit our store',
        'Flexible time slots',
        'Contact-free pickup/delivery available',
      ],
      additionalFee: 0.0,
    ),
    DeliveryOption(
      type: DeliveryType.inStorePickup,
      title: 'In-Store Pickup',
      description: 'Drop off at our store, pick up when ready',
      icon: Icons.store,
      features: [
        'Drop off at your convenience',
        'Pick up when ready',
        'No delivery fees',
        'Extended store hours',
      ],
      additionalFee: 0.0,
      discount: 5.0, // 5% discount for in-store pickup
    ),
    DeliveryOption(
      type: DeliveryType.inStoreDelivery,
      title: 'In-Store Drop-off & Delivery',
      description: 'Drop off at store, we deliver to your location',
      icon: Icons.storefront,
      features: [
        'Drop off at our store',
        'We deliver to your location',
        'Reduced service fee',
        'Flexible delivery slots',
      ],
      additionalFee: 2.50,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Delivery Options'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            child: Column(
              children: [
                Icon(
                  Icons.local_shipping,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 12),
                Text(
                  'Choose Delivery Method',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'How would you like to handle pickup and delivery?',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          // Delivery Options
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _deliveryOptions.length,
              itemBuilder: (context, index) {
                final option = _deliveryOptions[index];
                final isSelected = _selectedDeliveryType == option.type;
                
                return Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Card(
                    elevation: isSelected ? 8 : 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: BorderSide(
                        color: isSelected 
                          ? Theme.of(context).colorScheme.primary
                          : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _selectedDeliveryType = option.type;
                        });
                      },
                      borderRadius: BorderRadius.circular(16),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    option.icon,
                                    color: isSelected ? Colors.white : Theme.of(context).colorScheme.primary,
                                    size: 28,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        option.title,
                                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: isSelected ? Theme.of(context).colorScheme.primary : null,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        option.description,
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (isSelected)
                                  Icon(
                                    Icons.check_circle,
                                    color: Theme.of(context).colorScheme.primary,
                                    size: 28,
                                  ),
                              ],
                            ),
                            
                            const SizedBox(height: 16),
                            
                            // Pricing info
                            Row(
                              children: [
                                if (option.additionalFee > 0)
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.orange[100],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      '+\$${option.additionalFee.toStringAsFixed(2)} fee',
                                      style: TextStyle(
                                        color: Colors.orange[700],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                if (option.discount > 0)
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.green[100],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      '${option.discount.toStringAsFixed(0)}% OFF',
                                      style: TextStyle(
                                        color: Colors.green[700],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                if (option.additionalFee == 0 && option.discount == 0)
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.blue[100],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      'No extra fee',
                                      style: TextStyle(
                                        color: Colors.blue[700],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            
                            const SizedBox(height: 12),
                            
                            // Features
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: option.features.map((feature) {
                                return Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    feature,
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Store Information (for in-store options)
          if (_selectedDeliveryType != DeliveryType.pickupAndDelivery)
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.store, color: Colors.blue[700]),
                      const SizedBox(width: 8),
                      Text(
                        'Store Information',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '123 Main Street, Downtown\nOpen: Mon-Sat 8AM-8PM, Sun 10AM-6PM\nPhone: (*************',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
          
          // Continue Button
          Container(
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _continueToNextStep,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  _getNextStepText(),
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getNextStepText() {
    switch (_selectedDeliveryType) {
      case DeliveryType.pickupAndDelivery:
        return 'Continue to Address & Time';
      case DeliveryType.inStorePickup:
        return 'Continue to Time Slots';
      case DeliveryType.inStoreDelivery:
        return 'Continue to Address & Time';
    }
  }

  void _continueToNextStep() {
    final updatedOrder = LaundryOrder(
      serviceTypes: widget.order.serviceTypes,
      garments: widget.order.garments,
      hasPackageDiscount: widget.order.hasPackageDiscount,
      packageDiscountPercent: widget.order.packageDiscountPercent,
      deliveryType: _selectedDeliveryType,
    );

    if (_selectedDeliveryType == DeliveryType.pickupAndDelivery || 
        _selectedDeliveryType == DeliveryType.inStoreDelivery) {
      // Need address information
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AddressSelectionScreen(order: updatedOrder),
        ),
      );
    } else {
      // In-store pickup - go directly to time slots
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TimeSlotSelectionScreen(order: updatedOrder),
        ),
      );
    }
  }
}

class DeliveryOption {
  final DeliveryType type;
  final String title;
  final String description;
  final IconData icon;
  final List<String> features;
  final double additionalFee;
  final double discount;

  DeliveryOption({
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    required this.features,
    this.additionalFee = 0.0,
    this.discount = 0.0,
  });
}
