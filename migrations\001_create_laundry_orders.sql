-- Drop policies first
DROP POLICY IF EXISTS "Anyone can view time slots" ON time_slots;
DROP POLICY IF EXISTS "Users can delete garment items from their orders" ON garment_items;
DROP POLICY IF EXISTS "Users can update garment items from their orders" ON garment_items;
DROP POLICY IF EXISTS "Users can insert garment items to their orders" ON garment_items;
DROP POLICY IF EXISTS "Users can view garment items from their orders" ON garment_items;
DROP POLICY IF EXISTS "Users can update their own orders" ON laundry_orders;
DROP POLICY IF EXISTS "Users can insert their own orders" ON laundry_orders;
DROP POLICY IF EXISTS "Users can view their own orders" ON laundry_orders;
DROP POLICY IF EXISTS "Users can delete their own addresses" ON addresses;
DROP POLICY IF EXISTS "Users can update their own addresses" ON addresses;
DROP POLICY IF EXISTS "Users can insert their own addresses" ON addresses;
DROP POLICY IF EXISTS "Users can view their own addresses" ON addresses;

-- Drop custom service/garment policies if they exist
DROP POLICY IF EXISTS "Users can delete their own custom garments" ON custom_garments;
DROP POLICY IF EXISTS "Users can update their own custom garments" ON custom_garments;
DROP POLICY IF EXISTS "Users can insert their own custom garments" ON custom_garments;
DROP POLICY IF EXISTS "Users can view their own custom garments" ON custom_garments;
DROP POLICY IF EXISTS "Users can delete their own custom services" ON custom_services;
DROP POLICY IF EXISTS "Users can update their own custom services" ON custom_services;
DROP POLICY IF EXISTS "Users can insert their own custom services" ON custom_services;
DROP POLICY IF EXISTS "Users can view their own custom services" ON custom_services;

-- Drop triggers (including custom service/garment triggers)
DROP TRIGGER IF EXISTS trigger_time_slots_updated_at ON time_slots;
DROP TRIGGER IF EXISTS trigger_garment_items_updated_at ON garment_items;
DROP TRIGGER IF EXISTS trigger_laundry_orders_updated_at ON laundry_orders;
DROP TRIGGER IF EXISTS trigger_addresses_updated_at ON addresses;
DROP TRIGGER IF EXISTS trigger_set_order_number ON laundry_orders;
DROP TRIGGER IF EXISTS trigger_custom_garments_updated_at ON custom_garments;
DROP TRIGGER IF EXISTS trigger_custom_services_updated_at ON custom_services;

-- Drop indexes
DROP INDEX IF EXISTS idx_addresses_user_id;
DROP INDEX IF EXISTS idx_garment_items_order_id;
DROP INDEX IF EXISTS idx_laundry_orders_delivery_time_slot_id;
DROP INDEX IF EXISTS idx_laundry_orders_pickup_time_slot_id;
DROP INDEX IF EXISTS idx_laundry_orders_delivery_address_id;
DROP INDEX IF EXISTS idx_laundry_orders_pickup_address_id;
DROP INDEX IF EXISTS idx_laundry_orders_user_id;
DROP INDEX IF EXISTS idx_time_slots_available;
DROP INDEX IF EXISTS idx_time_slots_date;
DROP INDEX IF EXISTS idx_laundry_orders_status;
DROP INDEX IF EXISTS idx_laundry_orders_created_at;
DROP INDEX IF EXISTS idx_custom_services_user_id;
DROP INDEX IF EXISTS idx_custom_services_service_type;
DROP INDEX IF EXISTS idx_custom_services_is_active;
DROP INDEX IF EXISTS idx_custom_garments_user_id;
DROP INDEX IF EXISTS idx_custom_garments_garment_type;
DROP INDEX IF EXISTS idx_custom_garments_is_active;

-- Drop tables (including custom service/garment tables)
DROP TABLE IF EXISTS custom_garments CASCADE;
DROP TABLE IF EXISTS custom_services CASCADE;
DROP TABLE IF EXISTS garment_items CASCADE;
DROP TABLE IF EXISTS laundry_orders CASCADE;
DROP TABLE IF EXISTS time_slots CASCADE;
DROP TABLE IF EXISTS addresses CASCADE;

-- Drop functions after all dependent objects are removed
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS set_order_number() CASCADE;
DROP FUNCTION IF EXISTS generate_order_number() CASCADE;

-- Drop types last
DROP TYPE IF EXISTS order_status CASCADE;
DROP TYPE IF EXISTS garment_type CASCADE;
DROP TYPE IF EXISTS delivery_type CASCADE;
DROP TYPE IF EXISTS pricing_type CASCADE;
DROP TYPE IF EXISTS service_type CASCADE;

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types
CREATE TYPE service_type AS ENUM ('wash_and_fold', 'dry_clean', 'ironing');
CREATE TYPE pricing_type AS ENUM ('per_piece', 'per_kg');
CREATE TYPE delivery_type AS ENUM ('pickup_and_delivery', 'in_store_pickup', 'in_store_delivery');
CREATE TYPE garment_type AS ENUM ('shirts', 'pants', 'dresses', 'suits', 'jackets', 'underwear', 'socks', 'bedding', 'towels', 'other');
CREATE TYPE order_status AS ENUM ('pending', 'confirmed', 'picked_up', 'in_progress', 'ready', 'delivered', 'cancelled');

-- Create addresses table
CREATE TABLE addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    street TEXT NOT NULL,
    apartment TEXT,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    zip_code TEXT NOT NULL,
    instructions TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create time_slots table
CREATE TABLE time_slots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    time_range TEXT NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    max_bookings INTEGER DEFAULT 10,
    current_bookings INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date, time_range)
);

-- Create laundry_orders table
CREATE TABLE laundry_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    order_number TEXT UNIQUE NOT NULL,
    service_types service_type[] NOT NULL,
    delivery_type delivery_type NOT NULL DEFAULT 'pickup_and_delivery',
    
    -- Address references
    pickup_address_id UUID REFERENCES addresses(id),
    delivery_address_id UUID REFERENCES addresses(id),
    
    -- Time slot references
    pickup_time_slot_id UUID REFERENCES time_slots(id),
    delivery_time_slot_id UUID REFERENCES time_slots(id),
    
    -- Pricing
    subtotal_price DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_price DECIMAL(10,2) DEFAULT 0.00,
    has_package_discount BOOLEAN DEFAULT FALSE,
    package_discount_percent DECIMAL(5,2) DEFAULT 0.00,
    
    -- Order details
    special_instructions TEXT,
    status order_status DEFAULT 'pending',
    total_items INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE,
    picked_up_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE
);

-- Create garment_items table
CREATE TABLE garment_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES laundry_orders(id) ON DELETE CASCADE,
    garment_type garment_type NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(8,2) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 0,
    weight DECIMAL(8,2), -- For per-kg pricing
    pricing_type pricing_type NOT NULL DEFAULT 'per_piece',
    is_custom BOOLEAN DEFAULT FALSE,
    total_price DECIMAL(10,2) GENERATED ALWAYS AS (
        CASE 
            WHEN pricing_type = 'per_kg' AND weight IS NOT NULL THEN price * weight
            ELSE price * quantity
        END
    ) STORED,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create custom_services table
CREATE TABLE custom_services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    service_type service_type NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    base_price DECIMAL(8,2) NOT NULL DEFAULT 0.00,
    pricing_type pricing_type NOT NULL DEFAULT 'per_piece',
    features TEXT[], -- Array of feature strings
    is_active BOOLEAN DEFAULT TRUE,
    icon_name TEXT DEFAULT 'cleaning_services', -- Material icon name
    color_hex TEXT DEFAULT '#2196F3', -- Hex color code
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, title) -- Prevent duplicate service names per user
);

-- Create custom_garments table
CREATE TABLE custom_garments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    service_type service_type NOT NULL, -- Which service this garment belongs to
    garment_type garment_type NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(8,2) NOT NULL,
    pricing_type pricing_type NOT NULL DEFAULT 'per_piece',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, service_type, name) -- Prevent duplicate garment names per user per service
);

-- Create indexes for better performance (with IF NOT EXISTS handling)
CREATE INDEX IF NOT EXISTS idx_laundry_orders_user_id ON laundry_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_laundry_orders_status ON laundry_orders(status);
CREATE INDEX IF NOT EXISTS idx_laundry_orders_created_at ON laundry_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_laundry_orders_pickup_address_id ON laundry_orders(pickup_address_id);
CREATE INDEX IF NOT EXISTS idx_laundry_orders_delivery_address_id ON laundry_orders(delivery_address_id);
CREATE INDEX IF NOT EXISTS idx_laundry_orders_pickup_time_slot_id ON laundry_orders(pickup_time_slot_id);
CREATE INDEX IF NOT EXISTS idx_laundry_orders_delivery_time_slot_id ON laundry_orders(delivery_time_slot_id);
CREATE INDEX IF NOT EXISTS idx_garment_items_order_id ON garment_items(order_id);
CREATE INDEX IF NOT EXISTS idx_addresses_user_id ON addresses(user_id);
CREATE INDEX IF NOT EXISTS idx_time_slots_date ON time_slots(date);
CREATE INDEX IF NOT EXISTS idx_time_slots_available ON time_slots(is_available);
CREATE INDEX IF NOT EXISTS idx_custom_services_user_id ON custom_services(user_id);
CREATE INDEX IF NOT EXISTS idx_custom_services_service_type ON custom_services(service_type);
CREATE INDEX IF NOT EXISTS idx_custom_services_is_active ON custom_services(is_active);
CREATE INDEX IF NOT EXISTS idx_custom_garments_user_id ON custom_garments(user_id);
CREATE INDEX IF NOT EXISTS idx_custom_garments_garment_type ON custom_garments(garment_type);
CREATE INDEX IF NOT EXISTS idx_custom_garments_is_active ON custom_garments(is_active);

-- Create function to generate order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    counter INTEGER;
BEGIN
    -- Get current date in YYYYMMDD format
    new_number := 'LO' || TO_CHAR(NOW(), 'YYYYMMDD');
    
    -- Get count of orders created today
    SELECT COUNT(*) + 1 INTO counter
    FROM laundry_orders 
    WHERE DATE(created_at) = CURRENT_DATE;
    
    -- Append counter with zero padding
    new_number := new_number || LPAD(counter::TEXT, 4, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql SET search_path = public;

-- Create trigger to auto-generate order numbers
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = public;

-- Create trigger for order number generation (with proper IF NOT EXISTS handling)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_set_order_number') THEN
        CREATE TRIGGER trigger_set_order_number
            BEFORE INSERT ON laundry_orders
            FOR EACH ROW
            EXECUTE FUNCTION set_order_number();
    END IF;
END $$;

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = public;

-- Create triggers for updated_at timestamps (with proper IF NOT EXISTS handling)
DO $$
BEGIN
    -- Create trigger for addresses if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_addresses_updated_at') THEN
        CREATE TRIGGER trigger_addresses_updated_at
            BEFORE UPDATE ON addresses
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Create trigger for laundry_orders if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_laundry_orders_updated_at') THEN
        CREATE TRIGGER trigger_laundry_orders_updated_at
            BEFORE UPDATE ON laundry_orders
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Create trigger for garment_items if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_garment_items_updated_at') THEN
        CREATE TRIGGER trigger_garment_items_updated_at
            BEFORE UPDATE ON garment_items
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Create trigger for time_slots if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_time_slots_updated_at') THEN
        CREATE TRIGGER trigger_time_slots_updated_at
            BEFORE UPDATE ON time_slots
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Create trigger for custom_services if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_custom_services_updated_at') THEN
        CREATE TRIGGER trigger_custom_services_updated_at
            BEFORE UPDATE ON custom_services
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Create trigger for custom_garments if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_custom_garments_updated_at') THEN
        CREATE TRIGGER trigger_custom_garments_updated_at
            BEFORE UPDATE ON custom_garments
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

CREATE TRIGGER trigger_time_slots_updated_at
    BEFORE UPDATE ON time_slots
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create custom_services table
CREATE TABLE custom_services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    title TEXT,
    description TEXT,
    price DECIMAL(8,2) NOT NULL,
    pricing_type pricing_type NOT NULL DEFAULT 'per_piece',
    service_type service_type NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create custom_garments table
CREATE TABLE custom_garments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(8,2) NOT NULL,
    pricing_type pricing_type NOT NULL DEFAULT 'per_piece',
    garment_type garment_type NOT NULL,
    service_type service_type,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create triggers for updated_at timestamps on custom tables
CREATE TRIGGER trigger_custom_services_updated_at
    BEFORE UPDATE ON custom_services
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_custom_garments_updated_at
    BEFORE UPDATE ON custom_garments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert some default time slots
INSERT INTO time_slots (date, time_range, max_bookings) VALUES
-- Today and next 7 days
(CURRENT_DATE, '8:00 AM - 10:00 AM', 10),
(CURRENT_DATE, '10:00 AM - 12:00 PM', 10),
(CURRENT_DATE, '12:00 PM - 2:00 PM', 10),
(CURRENT_DATE, '2:00 PM - 4:00 PM', 10),
(CURRENT_DATE, '4:00 PM - 6:00 PM', 10),
(CURRENT_DATE, '6:00 PM - 8:00 PM', 10),

(CURRENT_DATE + 1, '8:00 AM - 10:00 AM', 10),
(CURRENT_DATE + 1, '10:00 AM - 12:00 PM', 10),
(CURRENT_DATE + 1, '12:00 PM - 2:00 PM', 10),
(CURRENT_DATE + 1, '2:00 PM - 4:00 PM', 10),
(CURRENT_DATE + 1, '4:00 PM - 6:00 PM', 10),
(CURRENT_DATE + 1, '6:00 PM - 8:00 PM', 10),

(CURRENT_DATE + 2, '8:00 AM - 10:00 AM', 10),
(CURRENT_DATE + 2, '10:00 AM - 12:00 PM', 10),
(CURRENT_DATE + 2, '12:00 PM - 2:00 PM', 10),
(CURRENT_DATE + 2, '2:00 PM - 4:00 PM', 10),
(CURRENT_DATE + 2, '4:00 PM - 6:00 PM', 10),
(CURRENT_DATE + 2, '6:00 PM - 8:00 PM', 10),

(CURRENT_DATE + 3, '8:00 AM - 10:00 AM', 10),
(CURRENT_DATE + 3, '10:00 AM - 12:00 PM', 10),
(CURRENT_DATE + 3, '12:00 PM - 2:00 PM', 10),
(CURRENT_DATE + 3, '2:00 PM - 4:00 PM', 10),
(CURRENT_DATE + 3, '4:00 PM - 6:00 PM', 10),
(CURRENT_DATE + 3, '6:00 PM - 8:00 PM', 10),

(CURRENT_DATE + 4, '8:00 AM - 10:00 AM', 10),
(CURRENT_DATE + 4, '10:00 AM - 12:00 PM', 10),
(CURRENT_DATE + 4, '12:00 PM - 2:00 PM', 10),
(CURRENT_DATE + 4, '2:00 PM - 4:00 PM', 10),
(CURRENT_DATE + 4, '4:00 PM - 6:00 PM', 10),
(CURRENT_DATE + 4, '6:00 PM - 8:00 PM', 10),

(CURRENT_DATE + 5, '8:00 AM - 10:00 AM', 10),
(CURRENT_DATE + 5, '10:00 AM - 12:00 PM', 10),
(CURRENT_DATE + 5, '12:00 PM - 2:00 PM', 10),
(CURRENT_DATE + 5, '2:00 PM - 4:00 PM', 10),
(CURRENT_DATE + 5, '4:00 PM - 6:00 PM', 10),
(CURRENT_DATE + 5, '6:00 PM - 8:00 PM', 10),

(CURRENT_DATE + 6, '8:00 AM - 10:00 AM', 10),
(CURRENT_DATE + 6, '10:00 AM - 12:00 PM', 10),
(CURRENT_DATE + 6, '12:00 PM - 2:00 PM', 10),
(CURRENT_DATE + 6, '2:00 PM - 4:00 PM', 10),
(CURRENT_DATE + 6, '4:00 PM - 6:00 PM', 10),
(CURRENT_DATE + 6, '6:00 PM - 8:00 PM', 10),

(CURRENT_DATE + 7, '8:00 AM - 10:00 AM', 10),
(CURRENT_DATE + 7, '10:00 AM - 12:00 PM', 10),
(CURRENT_DATE + 7, '12:00 PM - 2:00 PM', 10),
(CURRENT_DATE + 7, '2:00 PM - 4:00 PM', 10),
(CURRENT_DATE + 7, '4:00 PM - 6:00 PM', 10),
(CURRENT_DATE + 7, '6:00 PM - 8:00 PM', 10);

-- Enable Row Level Security (RLS)
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE laundry_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE garment_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE time_slots ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_garments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies with optimized auth.uid() calls (with proper IF NOT EXISTS handling)
-- Users can only see their own addresses
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'addresses' AND policyname = 'Users can view their own addresses') THEN
        CREATE POLICY "Users can view their own addresses" ON addresses
            FOR SELECT USING ((select auth.uid()) = user_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'addresses' AND policyname = 'Users can insert their own addresses') THEN
        CREATE POLICY "Users can insert their own addresses" ON addresses
            FOR INSERT WITH CHECK ((select auth.uid()) = user_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'addresses' AND policyname = 'Users can update their own addresses') THEN
        CREATE POLICY "Users can update their own addresses" ON addresses
            FOR UPDATE USING ((select auth.uid()) = user_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'addresses' AND policyname = 'Users can delete their own addresses') THEN
        CREATE POLICY "Users can delete their own addresses" ON addresses
            FOR DELETE USING ((select auth.uid()) = user_id);
    END IF;
END $$;

-- Users can only see their own orders
CREATE POLICY "Users can view their own orders" ON laundry_orders
    FOR SELECT USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can insert their own orders" ON laundry_orders
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Users can update their own orders" ON laundry_orders
    FOR UPDATE USING ((select auth.uid()) = user_id);

-- Users can only see garment items from their own orders
CREATE POLICY "Users can view garment items from their orders" ON garment_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM laundry_orders 
            WHERE laundry_orders.id = garment_items.order_id 
            AND laundry_orders.user_id = (select auth.uid())
        )
    );

CREATE POLICY "Users can insert garment items to their orders" ON garment_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM laundry_orders 
            WHERE laundry_orders.id = garment_items.order_id 
            AND laundry_orders.user_id = (select auth.uid())
        )
    );

CREATE POLICY "Users can update garment items from their orders" ON garment_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM laundry_orders 
            WHERE laundry_orders.id = garment_items.order_id 
            AND laundry_orders.user_id = (select auth.uid())
        )
    );

CREATE POLICY "Users can delete garment items from their orders" ON garment_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM laundry_orders 
            WHERE laundry_orders.id = garment_items.order_id 
            AND laundry_orders.user_id = (select auth.uid())
        )
    );

-- Time slots are readable by everyone (no user_id)
CREATE POLICY "Anyone can view time slots" ON time_slots
    FOR SELECT USING (true);

-- Create RLS policies for custom_services
CREATE POLICY "Users can view their own custom services" ON custom_services
    FOR SELECT USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can insert their own custom services" ON custom_services
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Users can update their own custom services" ON custom_services
    FOR UPDATE USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can delete their own custom services" ON custom_services
    FOR DELETE USING ((select auth.uid()) = user_id);

-- Create RLS policies for custom_garments
CREATE POLICY "Users can view their own custom garments" ON custom_garments
    FOR SELECT USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can insert their own custom garments" ON custom_garments
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Users can update their own custom garments" ON custom_garments
    FOR UPDATE USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can delete their own custom garments" ON custom_garments
    FOR DELETE USING ((select auth.uid()) = user_id);

-- Create RLS policies for custom_services
CREATE POLICY "Users can view their own custom services" ON custom_services
    FOR SELECT USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can insert their own custom services" ON custom_services
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Users can update their own custom services" ON custom_services
    FOR UPDATE USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can delete their own custom services" ON custom_services
    FOR DELETE USING ((select auth.uid()) = user_id);

-- Create RLS policies for custom_garments
CREATE POLICY "Users can view their own custom garments" ON custom_garments
    FOR SELECT USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can insert their own custom garments" ON custom_garments
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Users can update their own custom garments" ON custom_garments
    FOR UPDATE USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can delete their own custom garments" ON custom_garments
    FOR DELETE USING ((select auth.uid()) = user_id);