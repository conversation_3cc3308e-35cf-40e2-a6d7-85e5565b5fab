import 'package:flutter/material.dart';
import '../models/laundry_order.dart';
import '../widgets/custom_text_field.dart';
import 'delivery_type_selection_screen.dart';

class EnhancedGarmentSelectionScreen extends StatefulWidget {
  final LaundryOrder order;

  const EnhancedGarmentSelectionScreen({
    super.key,
    required this.order,
  });

  @override
  State<EnhancedGarmentSelectionScreen> createState() => _EnhancedGarmentSelectionScreenState();
}

class _EnhancedGarmentSelectionScreenState extends State<EnhancedGarmentSelectionScreen> {
  late List<GarmentItem> _garments;
  bool _hasPackageDiscount = false;
  double _packageDiscountPercent = 10.0;
  
  @override
  void initState() {
    super.initState();
    _garments = GarmentDefinitions.getGarmentsForServices(widget.order.serviceTypes);
  }

  @override
  Widget build(BuildContext context) {
    final totalItems = _garments.fold(0, (sum, item) => sum + item.quantity);
    final subtotal = _garments.fold(0.0, (sum, item) => sum + item.totalPrice);
    final discountAmount = _hasPackageDiscount ? subtotal * (_packageDiscountPercent / 100) : 0.0;
    final totalPrice = subtotal - discountAmount;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Garments'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: _showAddCustomGarmentDialog,
            icon: const Icon(Icons.add),
            tooltip: 'Add Custom Item',
          ),
        ],
      ),
      body: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            child: Column(
              children: [
                Icon(
                  Icons.checkroom,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 12),
                Text(
                  'Add Your Garments',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Select items for your ${widget.order.serviceTypesDisplayName} order',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          // Garment List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _garments.length,
              itemBuilder: (context, index) {
                final garment = _garments[index];
                
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  _getGarmentIcon(garment.type),
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            garment.name,
                                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                        if (garment.isCustom)
                                          Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: Colors.orange[100],
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              'Custom',
                                              style: TextStyle(
                                                fontSize: 10,
                                                color: Colors.orange[700],
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      garment.description,
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  GestureDetector(
                                    onTap: () => _showEditPriceDialog(garment),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          garment.priceDisplay,
                                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color: Theme.of(context).colorScheme.primary,
                                          ),
                                        ),
                                        const SizedBox(width: 4),
                                        Icon(
                                          Icons.edit,
                                          size: 16,
                                          color: Colors.grey[600],
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (garment.pricingType == PricingType.perKg)
                                    Text(
                                      'By weight',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 16),
                          
                          if (garment.pricingType == PricingType.perPiece) ...[
                            // Quantity selector for per-piece items
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Quantity',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Row(
                                  children: [
                                    IconButton(
                                      onPressed: garment.quantity > 0 ? () {
                                        setState(() {
                                          garment.quantity--;
                                        });
                                      } : null,
                                      icon: const Icon(Icons.remove_circle_outline),
                                    ),
                                    Container(
                                      width: 50,
                                      padding: const EdgeInsets.symmetric(vertical: 8),
                                      child: Text(
                                        '${garment.quantity}',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        setState(() {
                                          garment.quantity++;
                                        });
                                      },
                                      icon: const Icon(Icons.add_circle_outline),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ] else ...[
                            // Weight input for per-kg items
                            Row(
                              children: [
                                Expanded(
                                  child: CustomTextField(
                                    controller: TextEditingController(
                                      text: garment.weight?.toStringAsFixed(1) ?? '',
                                    ),
                                    label: 'Weight (kg)',
                                    hintText: '0.0',
                                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                    onChanged: (value) {
                                      setState(() {
                                        garment.weight = double.tryParse(value) ?? 0.0;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                          
                          if ((garment.pricingType == PricingType.perPiece && garment.quantity > 0) ||
                              (garment.pricingType == PricingType.perKg && garment.weight != null && garment.weight! > 0)) ...[
                            const SizedBox(height: 8),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'Subtotal: \$${garment.totalPrice.toStringAsFixed(2)}',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Package Discount Section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Checkbox(
                      value: _hasPackageDiscount,
                      onChanged: (value) {
                        setState(() {
                          _hasPackageDiscount = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                      child: Text(
                        'Apply package discount',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    if (_hasPackageDiscount)
                      GestureDetector(
                        onTap: _showDiscountDialog,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${_packageDiscountPercent.toStringAsFixed(0)}% OFF',
                            style: TextStyle(
                              color: Colors.green[700],
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                if (_hasPackageDiscount)
                  Text(
                    'Save \$${discountAmount.toStringAsFixed(2)} on your order',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green[700],
                    ),
                  ),
              ],
            ),
          ),
          
          // Summary and Continue
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '$totalItems items',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (_hasPackageDiscount) ...[
                          Text(
                            'Subtotal: \$${subtotal.toStringAsFixed(2)}',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              decoration: TextDecoration.lineThrough,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            'Discount: -\$${discountAmount.toStringAsFixed(2)}',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.green[700],
                            ),
                          ),
                        ],
                        Text(
                          'Total: \$${totalPrice.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: totalItems > 0 ? _continueToDeliveryType : null,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Continue to Delivery Options',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getGarmentIcon(GarmentType type) {
    switch (type) {
      case GarmentType.shirts:
        return Icons.checkroom;
      case GarmentType.pants:
        return Icons.accessibility_new;
      case GarmentType.dresses:
        return Icons.woman;
      case GarmentType.suits:
        return Icons.business_center;
      case GarmentType.jackets:
        return Icons.checkroom;
      case GarmentType.underwear:
        return Icons.favorite;
      case GarmentType.socks:
        return Icons.sports_handball;
      case GarmentType.bedding:
        return Icons.bed;
      case GarmentType.towels:
        return Icons.dry;
      case GarmentType.other:
        return Icons.more_horiz;
    }
  }

  void _showAddCustomGarmentDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    final priceController = TextEditingController();
    PricingType selectedPricingType = PricingType.perPiece;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Add Custom Garment'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomTextField(
                  controller: nameController,
                  label: 'Garment Name',
                  hintText: 'e.g., Silk Scarf',
                ),
                const SizedBox(height: 16),
                CustomTextField(
                  controller: descriptionController,
                  label: 'Description',
                  hintText: 'Brief description',
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: CustomTextField(
                        controller: priceController,
                        label: 'Price',
                        hintText: '0.00',
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<PricingType>(
                        value: selectedPricingType,
                        decoration: const InputDecoration(
                          labelText: 'Pricing Type',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: PricingType.perPiece,
                            child: Text('Per Piece'),
                          ),
                          DropdownMenuItem(
                            value: PricingType.perKg,
                            child: Text('Per Kg'),
                          ),
                        ],
                        onChanged: (value) {
                          setDialogState(() {
                            selectedPricingType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.isNotEmpty && priceController.text.isNotEmpty) {
                  final customGarment = GarmentDefinitions.createCustomGarment(
                    name: nameController.text,
                    description: descriptionController.text,
                    price: double.tryParse(priceController.text) ?? 0.0,
                    pricingType: selectedPricingType,
                  );
                  setState(() {
                    _garments.add(customGarment);
                  });
                  Navigator.pop(context);
                }
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditPriceDialog(GarmentItem garment) {
    final priceController = TextEditingController(text: garment.price.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${garment.name}'),
        content: CustomTextField(
          controller: priceController,
          label: 'Price',
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                garment.price = double.tryParse(priceController.text) ?? garment.price;
              });
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showDiscountDialog() {
    final discountController = TextEditingController(text: _packageDiscountPercent.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Package Discount'),
        content: CustomTextField(
          controller: discountController,
          label: 'Discount Percentage',
          hintText: '10',
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _packageDiscountPercent = double.tryParse(discountController.text) ?? _packageDiscountPercent;
              });
              Navigator.pop(context);
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _continueToDeliveryType() {
    final selectedGarments = _garments.where((g) =>
      g.quantity > 0 || (g.weight != null && g.weight! > 0)
    ).toList();

    final updatedOrder = LaundryOrder(
      serviceTypes: widget.order.serviceTypes,
      garments: selectedGarments,
      hasPackageDiscount: _hasPackageDiscount,
      packageDiscountPercent: _packageDiscountPercent,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DeliveryTypeSelectionScreen(order: updatedOrder),
      ),
    );
  }
}