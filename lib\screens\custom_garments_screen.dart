import 'package:flutter/material.dart';
import '../services/custom_service_management_service.dart';
import '../models/laundry_order.dart';
import '../utils/snackbar_helper.dart';
import 'add_edit_garment_screen.dart';

class CustomGarmentsScreen extends StatefulWidget {
  const CustomGarmentsScreen({super.key});

  @override
  State<CustomGarmentsScreen> createState() => _CustomGarmentsScreenState();
}

class _CustomGarmentsScreenState extends State<CustomGarmentsScreen> {
  List<Map<String, dynamic>> _garments = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadGarments();
  }

  Future<void> _loadGarments() async {
    try {
      final garments = await CustomServiceManagementService.getUserCustomGarments();
      if (mounted) {
        setState(() {
          _garments = garments;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        SnackbarHelper.showError(
          context,
          'Failed to load garments: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _deleteGarment(String garmentId, String garmentName) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Garment'),
        content: Text('Are you sure you want to delete "$garmentName"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await CustomServiceManagementService.deleteCustomGarment(garmentId);
        SnackbarHelper.showSuccess(context, 'Garment deleted successfully');
        _loadGarments(); // Reload the list
      } catch (e) {
        SnackbarHelper.showError(context, 'Failed to delete garment: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Custom Garments'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddEditGarmentScreen(),
                ),
              );
              if (result == true) {
                _loadGarments(); // Reload if garment was added
              }
            },
            icon: const Icon(Icons.add),
            tooltip: 'Add Garment',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _garments.isEmpty
              ? _buildEmptyState()
              : _buildGarmentsList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddEditGarmentScreen(),
            ),
          );
          if (result == true) {
            _loadGarments(); // Reload if garment was added
          }
        },
        child: const Icon(Icons.add),
        tooltip: 'Add Garment',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.checkroom_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Custom Garments',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first custom garment to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddEditGarmentScreen(),
                  ),
                );
                if (result == true) {
                  _loadGarments();
                }
              },
              icon: const Icon(Icons.add),
              label: const Text('Add Garment'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGarmentsList() {
    // Group garments by service type
    final groupedGarments = <String, List<Map<String, dynamic>>>{};
    for (final garment in _garments) {
      final serviceType = garment['service_type'] as String;
      groupedGarments.putIfAbsent(serviceType, () => []).add(garment);
    }

    return RefreshIndicator(
      onRefresh: _loadGarments,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: groupedGarments.length,
        itemBuilder: (context, index) {
          final serviceType = groupedGarments.keys.elementAt(index);
          final garments = groupedGarments[serviceType]!;
          
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Service Type Header
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Text(
                  _getServiceTypeDisplayName(serviceType),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              
              // Garments in this service type
              ...garments.map((garment) => _buildGarmentCard(garment)),
              
              const SizedBox(height: 16),
            ],
          );
        },
      ),
    );
  }

  Widget _buildGarmentCard(Map<String, dynamic> garment) {
    final name = garment['name'] as String;
    final description = garment['description'] as String? ?? '';
    final price = (garment['price'] as num).toDouble();
    final pricingType = garment['pricing_type'] as String;
    final garmentType = garment['garment_type'] as String;
    final garmentId = garment['id'] as String;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getGarmentTypeColor(garmentType).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getGarmentTypeIcon(garmentType),
                    color: _getGarmentTypeColor(garmentType),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getGarmentTypeDisplayName(garmentType),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[500],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (description.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          description,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AddEditGarmentScreen(garmentId: garmentId),
                        ),
                      ).then((result) {
                        if (result == true) {
                          _loadGarments();
                        }
                      });
                    } else if (value == 'delete') {
                      _deleteGarment(garmentId, name);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Text(
              '\$${price.toStringAsFixed(2)}/${pricingType == 'per_piece' ? 'item' : 'kg'}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getServiceTypeDisplayName(String serviceType) {
    switch (serviceType) {
      case 'wash_and_fold':
        return 'Wash & Fold Garments';
      case 'dry_clean':
        return 'Dry Clean Garments';
      case 'ironing':
        return 'Ironing Garments';
      default:
        return serviceType.toUpperCase();
    }
  }

  String _getGarmentTypeDisplayName(String garmentType) {
    switch (garmentType) {
      case 'shirts':
        return 'Shirts';
      case 'pants':
        return 'Pants';
      case 'dresses':
        return 'Dresses';
      case 'suits':
        return 'Suits';
      case 'jackets':
        return 'Jackets';
      case 'underwear':
        return 'Underwear';
      case 'socks':
        return 'Socks';
      case 'bedding':
        return 'Bedding';
      case 'towels':
        return 'Towels';
      case 'other':
        return 'Other';
      default:
        return garmentType.toUpperCase();
    }
  }

  IconData _getGarmentTypeIcon(String garmentType) {
    switch (garmentType) {
      case 'shirts':
        return Icons.checkroom;
      case 'pants':
        return Icons.checkroom;
      case 'dresses':
        return Icons.checkroom;
      case 'suits':
        return Icons.business_center;
      case 'jackets':
        return Icons.checkroom;
      case 'underwear':
        return Icons.checkroom;
      case 'socks':
        return Icons.checkroom;
      case 'bedding':
        return Icons.bed;
      case 'towels':
        return Icons.dry;
      case 'other':
        return Icons.category;
      default:
        return Icons.checkroom;
    }
  }

  Color _getGarmentTypeColor(String garmentType) {
    switch (garmentType) {
      case 'shirts':
        return Colors.blue;
      case 'pants':
        return Colors.green;
      case 'dresses':
        return Colors.pink;
      case 'suits':
        return Colors.purple;
      case 'jackets':
        return Colors.orange;
      case 'underwear':
        return Colors.teal;
      case 'socks':
        return Colors.indigo;
      case 'bedding':
        return Colors.brown;
      case 'towels':
        return Colors.cyan;
      case 'other':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }
}
