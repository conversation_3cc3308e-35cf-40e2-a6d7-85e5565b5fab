import 'package:flutter/material.dart';
import 'enhanced_garment_selection_screen.dart';
import '../models/laundry_order.dart';

class ServiceSelectionScreen extends StatefulWidget {
  const ServiceSelectionScreen({super.key});

  @override
  State<ServiceSelectionScreen> createState() => _ServiceSelectionScreenState();
}

class _ServiceSelectionScreenState extends State<ServiceSelectionScreen> {
  final Set<ServiceType> _selectedServices = <ServiceType>{};

  final List<ServiceOption> _services = [
    ServiceOption(
      type: ServiceType.washAndFold,
      title: 'Wash & Fold',
      description: 'Professional washing, drying, and folding',
      icon: Icons.local_laundry_service,
      price: 'Starting at \$2.50/lb',
      features: [
        'Eco-friendly detergents',
        'Fabric softener included',
        'Folded and packaged',
        '24-48 hour turnaround',
      ],
    ),
    ServiceOption(
      type: ServiceType.dryClean,
      title: 'Dry Clean',
      description: 'Professional dry cleaning for delicate items',
      icon: Icons.dry_cleaning,
      price: 'Starting at \$8.99/item',
      features: [
        'Professional dry cleaning',
        'Stain removal',
        'Pressed and hung',
        '2-3 day turnaround',
      ],
    ),
    ServiceOption(
      type: ServiceType.ironing,
      title: 'Ironing',
      description: 'Professional pressing and ironing service',
      icon: Icons.iron,
      price: 'Starting at \$3.99/item',
      features: [
        'Professional pressing',
        'Wrinkle removal',
        'Hung on hangers',
        'Same day service',
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Service'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            child: Column(
              children: [
                Icon(
                  Icons.cleaning_services,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 12),
                Text(
                  'Choose Your Services',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Select one or more laundry services (you can combine services)',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          // Service Options
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _services.length,
              itemBuilder: (context, index) {
                final service = _services[index];
                final isSelected = _selectedServices.contains(service.type);
                
                return Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Card(
                    elevation: isSelected ? 8 : 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: BorderSide(
                        color: isSelected 
                          ? Theme.of(context).colorScheme.primary
                          : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          if (_selectedServices.contains(service.type)) {
                            _selectedServices.remove(service.type);
                          } else {
                            _selectedServices.add(service.type);
                          }
                        });
                      },
                      borderRadius: BorderRadius.circular(16),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    service.icon,
                                    color: isSelected ? Colors.white : Theme.of(context).colorScheme.primary,
                                    size: 28,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        service.title,
                                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: isSelected ? Theme.of(context).colorScheme.primary : null,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        service.description,
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (isSelected)
                                  Icon(
                                    Icons.check_circle,
                                    color: Theme.of(context).colorScheme.primary,
                                    size: 28,
                                  ),
                              ],
                            ),
                            
                            const SizedBox(height: 16),
                            
                            Text(
                              service.price,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            
                            const SizedBox(height: 12),
                            
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: service.features.map((feature) {
                                return Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    feature,
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Continue Button
          Container(
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedServices.isNotEmpty ? _continueToGarments : null,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  _selectedServices.isEmpty
                    ? 'Select at least one service'
                    : 'Continue with ${_selectedServices.length} service${_selectedServices.length > 1 ? 's' : ''}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _continueToGarments() {
    if (_selectedServices.isNotEmpty) {
      final order = LaundryOrder(serviceTypes: _selectedServices.toList());
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => EnhancedGarmentSelectionScreen(order: order),
        ),
      );
    }
  }
}

class ServiceOption {
  final ServiceType type;
  final String title;
  final String description;
  final IconData icon;
  final String price;
  final List<String> features;

  ServiceOption({
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    required this.price,
    required this.features,
  });
}
