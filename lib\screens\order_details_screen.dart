import 'package:flutter/material.dart';
import '../services/laundry_order_service.dart';
import '../utils/snackbar_helper.dart';
import 'edit_order_screen.dart';

class OrderDetailsScreen extends StatefulWidget {
  final String orderId;
  final String orderNumber;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
    required this.orderNumber,
  });

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  Map<String, dynamic>? _orderDetails;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadOrderDetails();
  }

  Future<void> _loadOrderDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final details = await LaundryOrderService.getOrderById(widget.orderId);
      
      if (details == null) {
        setState(() {
          _error = 'Order not found';
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _orderDetails = details;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load order details: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _cancelOrder() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: const Text('Are you sure you want to cancel this order? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Keep Order'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Cancel Order'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await LaundryOrderService.updateOrderStatus(widget.orderId, 'cancelled');
        if (mounted) {
          SnackbarHelper.showSuccess(context, 'Order cancelled successfully');
          Navigator.pop(context, true); // Return true to indicate order was updated
        }
      } catch (e) {
        if (mounted) {
          SnackbarHelper.showError(context, 'Failed to cancel order: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Order #${widget.orderNumber}'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_orderDetails != null && _orderDetails!['status'] == 'pending')
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _navigateToEditOrder();
                    break;
                  case 'cancel':
                    _cancelOrder();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('Edit Order'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'cancel',
                  child: Row(
                    children: [
                      Icon(Icons.cancel, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Cancel Order', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading order details...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Order',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.red[700],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _error!,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadOrderDetails,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_orderDetails == null) {
      return const Center(
        child: Text('Order not found'),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrderDetails,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOrderStatusCard(),
            const SizedBox(height: 16),
            _buildOrderSummaryCard(),
            const SizedBox(height: 16),
            _buildGarmentItemsCard(),
            const SizedBox(height: 16),
            _buildAddressesCard(),
            const SizedBox(height: 16),
            _buildTimeSlotsCard(),
            if (_orderDetails!['special_instructions'] != null) ...[
              const SizedBox(height: 16),
              _buildSpecialInstructionsCard(),
            ],
            const SizedBox(height: 16),
            _buildPricingCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderStatusCard() {
    final status = _orderDetails!['status'] as String;
    final createdAt = DateTime.parse(_orderDetails!['created_at'] as String);
    
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (status) {
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = Icons.schedule;
        statusText = 'Pending Confirmation';
        break;
      case 'confirmed':
        statusColor = Colors.blue;
        statusIcon = Icons.check_circle_outline;
        statusText = 'Confirmed';
        break;
      case 'picked_up':
        statusColor = Colors.purple;
        statusIcon = Icons.local_shipping;
        statusText = 'Picked Up';
        break;
      case 'in_progress':
        statusColor = Colors.indigo;
        statusIcon = Icons.cleaning_services;
        statusText = 'In Progress';
        break;
      case 'ready':
        statusColor = Colors.green;
        statusIcon = Icons.done_all;
        statusText = 'Ready for Delivery';
        break;
      case 'delivered':
        statusColor = Colors.teal;
        statusIcon = Icons.home;
        statusText = 'Delivered';
        break;
      case 'cancelled':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        statusText = 'Cancelled';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help_outline;
        statusText = status.toUpperCase();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    statusIcon,
                    color: statusColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        statusText,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: statusColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Order #${widget.orderNumber}',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Created: ${createdAt.day}/${createdAt.month}/${createdAt.year} at ${createdAt.hour}:${createdAt.minute.toString().padLeft(2, '0')}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummaryCard() {
    final serviceTypes = List<String>.from(_orderDetails!['service_types'] as List);
    final deliveryType = _orderDetails!['delivery_type'] as String;
    final totalItems = _orderDetails!['total_items'] as int? ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Summary',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Services', serviceTypes.map((s) => s.replaceAll('_', ' ').toUpperCase()).join(', ')),
            _buildInfoRow('Delivery Type', deliveryType.replaceAll('_', ' ').toUpperCase()),
            _buildInfoRow('Total Items', totalItems.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildGarmentItemsCard() {
    final garmentItems = _orderDetails!['garment_items'] as List<dynamic>? ?? [];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Garment Items',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (garmentItems.isEmpty)
              const Text('No garment items added yet')
            else
              ...garmentItems.map((item) => _buildGarmentItem(item)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildGarmentItem(Map<String, dynamic> item) {
    final name = item['name'] as String;
    final quantity = item['quantity'] as int;
    final price = (item['price'] as num).toDouble();
    final totalPrice = (item['total_price'] as num).toDouble();
    final pricingType = item['pricing_type'] as String;
    final weight = item['weight'] as num?;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                if (pricingType == 'per_kg' && weight != null)
                  Text('Weight: ${weight}kg × \$${price.toStringAsFixed(2)}/kg')
                else
                  Text('Quantity: $quantity × \$${price.toStringAsFixed(2)}/piece'),
              ],
            ),
          ),
          Text(
            '\$${totalPrice.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressesCard() {
    final pickupAddress = _orderDetails!['pickup_address'] as Map<String, dynamic>?;
    final deliveryAddress = _orderDetails!['delivery_address'] as Map<String, dynamic>?;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Addresses',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (pickupAddress != null) ...[
              _buildAddressSection('Pickup Address', pickupAddress),
              const SizedBox(height: 12),
            ],
            if (deliveryAddress != null)
              _buildAddressSection('Delivery Address', deliveryAddress),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressSection(String title, Map<String, dynamic> address) {
    final street = address['street'] as String;
    final apartment = address['apartment'] as String?;
    final city = address['city'] as String;
    final state = address['state'] as String;
    final zipCode = address['zip_code'] as String;
    final instructions = address['instructions'] as String?;

    String fullAddress = street;
    if (apartment != null && apartment.isNotEmpty) {
      fullAddress += ', $apartment';
    }
    fullAddress += ', $city, $state $zipCode';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        Text(fullAddress),
        if (instructions != null && instructions.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            'Instructions: $instructions',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTimeSlotsCard() {
    final pickupTimeSlot = _orderDetails!['pickup_time_slot'] as Map<String, dynamic>?;
    final deliveryTimeSlot = _orderDetails!['delivery_time_slot'] as Map<String, dynamic>?;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Time Slots',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (pickupTimeSlot != null) ...[
              _buildTimeSlotSection('Pickup Time', pickupTimeSlot),
              const SizedBox(height: 12),
            ],
            if (deliveryTimeSlot != null)
              _buildTimeSlotSection('Delivery Time', deliveryTimeSlot),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSlotSection(String title, Map<String, dynamic> timeSlot) {
    final date = DateTime.parse(timeSlot['date'] as String);
    final timeRange = timeSlot['time_range'] as String;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        Text('${date.day}/${date.month}/${date.year} - $timeRange'),
      ],
    );
  }

  Widget _buildSpecialInstructionsCard() {
    final instructions = _orderDetails!['special_instructions'] as String;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Special Instructions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(instructions),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingCard() {
    final subtotal = (_orderDetails!['subtotal_price'] as num).toDouble();
    final discount = (_orderDetails!['discount_amount'] as num).toDouble();
    final total = (_orderDetails!['total_price'] as num).toDouble();
    final hasPackageDiscount = _orderDetails!['has_package_discount'] as bool;
    final packageDiscountPercent = (_orderDetails!['package_discount_percent'] as num).toDouble();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pricing Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildPriceRow('Subtotal', subtotal),
            if (discount > 0) ...[
              _buildPriceRow('Discount', -discount, isDiscount: true),
              if (hasPackageDiscount)
                Text(
                  'Package discount: ${packageDiscountPercent.toStringAsFixed(1)}%',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.green[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
            ],
            const Divider(),
            _buildPriceRow('Total', total, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, double amount, {bool isDiscount = false, bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount < 0 ? '-' : ''}\$${amount.abs().toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isDiscount ? Colors.green[600] : (isTotal ? Theme.of(context).colorScheme.primary : null),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToEditOrder() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditOrderScreen(
          orderId: widget.orderId,
          orderDetails: _orderDetails!,
        ),
      ),
    ).then((updated) {
      if (updated == true) {
        _loadOrderDetails(); // Refresh the order details
      }
    });
  }
}
