// Store Details Model
class StoreDetails {
  final String? id;
  final String? userId;
  final String storeName;
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String phoneNumber;
  final String? email;
  final Map<String, dynamic> businessHours;
  final String? website;
  final String description;
  final List<String> servicesOffered;
  final double pickupRadius;
  final double minimumOrderAmount;
  final double deliveryFee;
  final double freeDeliveryThreshold;
  final double taxRate;
  final String currencySymbol;
  final String timezone;
  final String? logoUrl;
  final String? bannerUrl;
  final Map<String, dynamic> socialMedia;
  final Map<String, dynamic> notificationSettings;
  final List<String> paymentMethods;
  final String? specialInstructions;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  StoreDetails({
    this.id,
    this.userId,
    required this.storeName,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.phoneNumber,
    this.email,
    required this.businessHours,
    this.website,
    required this.description,
    required this.servicesOffered,
    required this.pickupRadius,
    required this.minimumOrderAmount,
    required this.deliveryFee,
    required this.freeDeliveryThreshold,
    required this.taxRate,
    required this.currencySymbol,
    required this.timezone,
    this.logoUrl,
    this.bannerUrl,
    required this.socialMedia,
    required this.notificationSettings,
    required this.paymentMethods,
    this.specialInstructions,
    required this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  factory StoreDetails.fromJson(Map<String, dynamic> json) {
    return StoreDetails(
      id: json['id'],
      userId: json['user_id'],
      storeName: json['store_name'] ?? 'My Laundry Shop',
      address: json['address'] ?? '123 Main Street',
      city: json['city'] ?? 'Your City',
      state: json['state'] ?? 'Your State',
      zipCode: json['zip_code'] ?? '12345',
      phoneNumber: json['phone_number'] ?? '(*************',
      email: json['email'],
      businessHours: json['business_hours'] ?? _defaultBusinessHours(),
      website: json['website'],
      description: json['description'] ?? 'Professional laundry and dry cleaning services',
      servicesOffered: List<String>.from(json['services_offered'] ?? ['Wash & Fold', 'Dry Cleaning', 'Ironing']),
      pickupRadius: (json['pickup_radius'] ?? 10.0).toDouble(),
      minimumOrderAmount: (json['minimum_order_amount'] ?? 20.0).toDouble(),
      deliveryFee: (json['delivery_fee'] ?? 5.0).toDouble(),
      freeDeliveryThreshold: (json['free_delivery_threshold'] ?? 50.0).toDouble(),
      taxRate: (json['tax_rate'] ?? 0.0875).toDouble(),
      currencySymbol: json['currency_symbol'] ?? '\$',
      timezone: json['timezone'] ?? 'America/New_York',
      logoUrl: json['logo_url'],
      bannerUrl: json['banner_url'],
      socialMedia: json['social_media'] ?? {},
      notificationSettings: json['notification_settings'] ?? _defaultNotificationSettings(),
      paymentMethods: List<String>.from(json['payment_methods'] ?? ['Credit Card', 'Debit Card', 'Cash']),
      specialInstructions: json['special_instructions'],
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'store_name': storeName,
      'address': address,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'phone_number': phoneNumber,
      'email': email,
      'business_hours': businessHours,
      'website': website,
      'description': description,
      'services_offered': servicesOffered,
      'pickup_radius': pickupRadius,
      'minimum_order_amount': minimumOrderAmount,
      'delivery_fee': deliveryFee,
      'free_delivery_threshold': freeDeliveryThreshold,
      'tax_rate': taxRate,
      'currency_symbol': currencySymbol,
      'timezone': timezone,
      'logo_url': logoUrl,
      'banner_url': bannerUrl,
      'social_media': socialMedia,
      'notification_settings': notificationSettings,
      'payment_methods': paymentMethods,
      'special_instructions': specialInstructions,
      'is_active': isActive,
    };
  }

  StoreDetails copyWith({
    String? id,
    String? userId,
    String? storeName,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? phoneNumber,
    String? email,
    Map<String, dynamic>? businessHours,
    String? website,
    String? description,
    List<String>? servicesOffered,
    double? pickupRadius,
    double? minimumOrderAmount,
    double? deliveryFee,
    double? freeDeliveryThreshold,
    double? taxRate,
    String? currencySymbol,
    String? timezone,
    String? logoUrl,
    String? bannerUrl,
    Map<String, dynamic>? socialMedia,
    Map<String, dynamic>? notificationSettings,
    List<String>? paymentMethods,
    String? specialInstructions,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return StoreDetails(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      storeName: storeName ?? this.storeName,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      businessHours: businessHours ?? this.businessHours,
      website: website ?? this.website,
      description: description ?? this.description,
      servicesOffered: servicesOffered ?? this.servicesOffered,
      pickupRadius: pickupRadius ?? this.pickupRadius,
      minimumOrderAmount: minimumOrderAmount ?? this.minimumOrderAmount,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      freeDeliveryThreshold: freeDeliveryThreshold ?? this.freeDeliveryThreshold,
      taxRate: taxRate ?? this.taxRate,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      timezone: timezone ?? this.timezone,
      logoUrl: logoUrl ?? this.logoUrl,
      bannerUrl: bannerUrl ?? this.bannerUrl,
      socialMedia: socialMedia ?? this.socialMedia,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      paymentMethods: paymentMethods ?? this.paymentMethods,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  static Map<String, dynamic> _defaultBusinessHours() {
    return {
      'monday': {'open': '08:00', 'close': '20:00', 'closed': false},
      'tuesday': {'open': '08:00', 'close': '20:00', 'closed': false},
      'wednesday': {'open': '08:00', 'close': '20:00', 'closed': false},
      'thursday': {'open': '08:00', 'close': '20:00', 'closed': false},
      'friday': {'open': '08:00', 'close': '20:00', 'closed': false},
      'saturday': {'open': '08:00', 'close': '20:00', 'closed': false},
      'sunday': {'open': '10:00', 'close': '18:00', 'closed': false},
    };
  }

  static Map<String, dynamic> _defaultNotificationSettings() {
    return {
      'email_notifications': true,
      'sms_notifications': false,
      'order_confirmations': true,
      'pickup_reminders': true,
      'delivery_notifications': true,
      'promotional_emails': false,
    };
  }

  String get fullAddress => '$address, $city, $state $zipCode';

  // Create a default store details instance
  static StoreDetails createDefault() {
    return StoreDetails(
      storeName: 'My Laundry Shop',
      address: '123 Main Street',
      city: 'Your City',
      state: 'Your State',
      zipCode: '12345',
      phoneNumber: '(*************',
      businessHours: _defaultBusinessHours(),
      description: 'Professional laundry and dry cleaning services',
      servicesOffered: ['Wash & Fold', 'Dry Cleaning', 'Ironing'],
      pickupRadius: 10.0,
      minimumOrderAmount: 20.0,
      deliveryFee: 5.0,
      freeDeliveryThreshold: 50.0,
      taxRate: 0.0875,
      currencySymbol: '\$',
      timezone: 'America/New_York',
      socialMedia: {},
      notificationSettings: _defaultNotificationSettings(),
      paymentMethods: ['Credit Card', 'Debit Card', 'Cash'],
      isActive: true,
    );
  }
}
