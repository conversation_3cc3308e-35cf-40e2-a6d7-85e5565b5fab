import 'package:flutter/material.dart';
import '../models/laundry_order.dart';
import '../widgets/custom_text_field.dart';
import 'time_slot_selection_screen.dart';

class AddressSelectionScreen extends StatefulWidget {
  final LaundryOrder order;

  const AddressSelectionScreen({
    super.key,
    required this.order,
  });

  @override
  State<AddressSelectionScreen> createState() => _AddressSelectionScreenState();
}

class _AddressSelectionScreenState extends State<AddressSelectionScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Pickup Address Controllers
  final _pickupStreetController = TextEditingController();
  final _pickupApartmentController = TextEditingController();
  final _pickupCityController = TextEditingController();
  final _pickupStateController = TextEditingController();
  final _pickupZipController = TextEditingController();
  final _pickupInstructionsController = TextEditingController();
  
  // Delivery Address Controllers
  final _deliveryStreetController = TextEditingController();
  final _deliveryApartmentController = TextEditingController();
  final _deliveryCityController = TextEditingController();
  final _deliveryStateController = TextEditingController();
  final _deliveryZipController = TextEditingController();
  final _deliveryInstructionsController = TextEditingController();
  
  bool _sameAsPickup = true;

  @override
  void dispose() {
    _pickupStreetController.dispose();
    _pickupApartmentController.dispose();
    _pickupCityController.dispose();
    _pickupStateController.dispose();
    _pickupZipController.dispose();
    _pickupInstructionsController.dispose();
    _deliveryStreetController.dispose();
    _deliveryApartmentController.dispose();
    _deliveryCityController.dispose();
    _deliveryStateController.dispose();
    _deliveryZipController.dispose();
    _deliveryInstructionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pickup & Delivery'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              child: Column(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Set Addresses',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getAddressInstructions(),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Pickup Address Section
                    Text(
                      'Pickup Address',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    CustomTextField(
                      controller: _pickupStreetController,
                      label: 'Street Address',
                      hintText: '123 Main Street',
                      prefixIcon: Icons.home,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter street address';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    CustomTextField(
                      controller: _pickupApartmentController,
                      label: 'Apartment/Unit (Optional)',
                      hintText: 'Apt 2B, Unit 5, etc.',
                      prefixIcon: Icons.apartment,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: CustomTextField(
                            controller: _pickupCityController,
                            label: 'City',
                            hintText: 'New York',
                            prefixIcon: Icons.location_city,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter city';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: CustomTextField(
                            controller: _pickupStateController,
                            label: 'State',
                            hintText: 'NY',
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Required';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: CustomTextField(
                            controller: _pickupZipController,
                            label: 'ZIP Code',
                            hintText: '10001',
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Required';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    CustomTextField(
                      controller: _pickupInstructionsController,
                      label: 'Pickup Instructions (Optional)',
                      hintText: 'Ring doorbell, leave at door, etc.',
                      prefixIcon: Icons.note,
                      maxLines: 2,
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // Same as Pickup Checkbox
                    Row(
                      children: [
                        Checkbox(
                          value: _sameAsPickup,
                          onChanged: (value) {
                            setState(() {
                              _sameAsPickup = value ?? false;
                              if (_sameAsPickup) {
                                _copyPickupToDelivery();
                              } else {
                                _clearDeliveryAddress();
                              }
                            });
                          },
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _sameAsPickup = !_sameAsPickup;
                                if (_sameAsPickup) {
                                  _copyPickupToDelivery();
                                } else {
                                  _clearDeliveryAddress();
                                }
                              });
                            },
                            child: Text(
                              'Delivery address is same as pickup address',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Delivery Address Section
                    if (!_sameAsPickup) ...[
                      Text(
                        'Delivery Address',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      CustomTextField(
                        controller: _deliveryStreetController,
                        label: 'Street Address',
                        hintText: '456 Oak Avenue',
                        prefixIcon: Icons.home,
                        validator: (value) {
                          if (!_sameAsPickup && (value == null || value.isEmpty)) {
                            return 'Please enter street address';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      CustomTextField(
                        controller: _deliveryApartmentController,
                        label: 'Apartment/Unit (Optional)',
                        hintText: 'Apt 3C, Unit 10, etc.',
                        prefixIcon: Icons.apartment,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: CustomTextField(
                              controller: _deliveryCityController,
                              label: 'City',
                              hintText: 'New York',
                              prefixIcon: Icons.location_city,
                              validator: (value) {
                                if (!_sameAsPickup && (value == null || value.isEmpty)) {
                                  return 'Please enter city';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: CustomTextField(
                              controller: _deliveryStateController,
                              label: 'State',
                              hintText: 'NY',
                              validator: (value) {
                                if (!_sameAsPickup && (value == null || value.isEmpty)) {
                                  return 'Required';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: CustomTextField(
                              controller: _deliveryZipController,
                              label: 'ZIP Code',
                              hintText: '10001',
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (!_sameAsPickup && (value == null || value.isEmpty)) {
                                  return 'Required';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      CustomTextField(
                        controller: _deliveryInstructionsController,
                        label: 'Delivery Instructions (Optional)',
                        hintText: 'Ring doorbell, leave at door, etc.',
                        prefixIcon: Icons.note,
                        maxLines: 2,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            // Continue Button
            Container(
              padding: const EdgeInsets.all(24),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _continueToTimeSlots,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Continue to Time Slots',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _copyPickupToDelivery() {
    _deliveryStreetController.text = _pickupStreetController.text;
    _deliveryApartmentController.text = _pickupApartmentController.text;
    _deliveryCityController.text = _pickupCityController.text;
    _deliveryStateController.text = _pickupStateController.text;
    _deliveryZipController.text = _pickupZipController.text;
    _deliveryInstructionsController.text = _pickupInstructionsController.text;
  }

  void _clearDeliveryAddress() {
    _deliveryStreetController.clear();
    _deliveryApartmentController.clear();
    _deliveryCityController.clear();
    _deliveryStateController.clear();
    _deliveryZipController.clear();
    _deliveryInstructionsController.clear();
  }

  void _continueToTimeSlots() {
    if (!_formKey.currentState!.validate()) return;

    final pickupAddress = Address(
      street: _pickupStreetController.text.trim(),
      apartment: _pickupApartmentController.text.trim().isNotEmpty 
        ? _pickupApartmentController.text.trim() 
        : null,
      city: _pickupCityController.text.trim(),
      state: _pickupStateController.text.trim(),
      zipCode: _pickupZipController.text.trim(),
      instructions: _pickupInstructionsController.text.trim().isNotEmpty 
        ? _pickupInstructionsController.text.trim() 
        : null,
    );

    final deliveryAddress = _sameAsPickup ? pickupAddress : Address(
      street: _deliveryStreetController.text.trim(),
      apartment: _deliveryApartmentController.text.trim().isNotEmpty 
        ? _deliveryApartmentController.text.trim() 
        : null,
      city: _deliveryCityController.text.trim(),
      state: _deliveryStateController.text.trim(),
      zipCode: _deliveryZipController.text.trim(),
      instructions: _deliveryInstructionsController.text.trim().isNotEmpty 
        ? _deliveryInstructionsController.text.trim() 
        : null,
    );

    final updatedOrder = LaundryOrder(
      serviceTypes: widget.order.serviceTypes,
      garments: widget.order.garments,
      pickupAddress: pickupAddress,
      deliveryAddress: deliveryAddress,
      hasPackageDiscount: widget.order.hasPackageDiscount,
      packageDiscountPercent: widget.order.packageDiscountPercent,
      deliveryType: widget.order.deliveryType,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TimeSlotSelectionScreen(order: updatedOrder),
      ),
    );
  }

  String _getAddressInstructions() {
    switch (widget.order.deliveryType) {
      case DeliveryType.pickupAndDelivery:
        return 'Where should we pick up and deliver your laundry?';
      case DeliveryType.inStorePickup:
        return 'This should not be shown for in-store pickup';
      case DeliveryType.inStoreDelivery:
        return 'Where should we deliver your laundry after you drop it off at our store?';
    }
  }
}
