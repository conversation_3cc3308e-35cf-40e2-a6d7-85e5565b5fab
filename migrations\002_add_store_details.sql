-- Migration: Add store details table and clients policies
-- Created: 2025-01-31
-- Description: Adds store details table for settings and missing clients RLS policies

-- Drop existing objects if they exist (for rerunning migration)
DROP POLICY IF EXISTS "Users can delete their own clients" ON clients;
DROP POLICY IF EXISTS "Users can update their own clients" ON clients;
DROP POLICY IF EXISTS "Users can insert their own clients" ON clients;
DROP POLICY IF EXISTS "Users can view their own clients" ON clients;

DROP POLICY IF EXISTS "Users can delete their own store_details" ON store_details;
DROP POLICY IF EXISTS "Users can update their own store_details" ON store_details;
DROP POLICY IF EXISTS "Users can insert their own store_details" ON store_details;
DROP POLICY IF EXISTS "Users can view their own store_details" ON store_details;

DROP TRIGGER IF EXISTS trigger_store_details_updated_at ON store_details;
DROP TRIGGER IF EXISTS trigger_clients_updated_at ON clients;

DROP INDEX IF EXISTS idx_store_details_user_id;
DROP INDEX IF EXISTS idx_clients_user_id;

DROP TABLE IF EXISTS store_details CASCADE;

-- Create store_details table
CREATE TABLE store_details (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    store_name TEXT NOT NULL DEFAULT 'My Laundry Shop',
    address TEXT NOT NULL DEFAULT '123 Main Street',
    city TEXT NOT NULL DEFAULT 'Your City',
    state TEXT NOT NULL DEFAULT 'Your State',
    zip_code TEXT NOT NULL DEFAULT '12345',
    phone_number TEXT NOT NULL DEFAULT '(*************',
    email TEXT,
    business_hours JSONB DEFAULT '{
        "monday": {"open": "08:00", "close": "20:00", "closed": false},
        "tuesday": {"open": "08:00", "close": "20:00", "closed": false},
        "wednesday": {"open": "08:00", "close": "20:00", "closed": false},
        "thursday": {"open": "08:00", "close": "20:00", "closed": false},
        "friday": {"open": "08:00", "close": "20:00", "closed": false},
        "saturday": {"open": "08:00", "close": "20:00", "closed": false},
        "sunday": {"open": "10:00", "close": "18:00", "closed": false}
    }',
    website TEXT,
    description TEXT DEFAULT 'Professional laundry and dry cleaning services',
    services_offered TEXT[] DEFAULT ARRAY['Wash & Fold', 'Dry Cleaning', 'Ironing'],
    pickup_radius DECIMAL(5,2) DEFAULT 10.00, -- in miles/km
    minimum_order_amount DECIMAL(8,2) DEFAULT 20.00,
    delivery_fee DECIMAL(6,2) DEFAULT 5.00,
    free_delivery_threshold DECIMAL(8,2) DEFAULT 50.00,
    tax_rate DECIMAL(5,4) DEFAULT 0.0875, -- 8.75%
    currency_symbol TEXT DEFAULT '$',
    timezone TEXT DEFAULT 'America/New_York',
    logo_url TEXT,
    banner_url TEXT,
    social_media JSONB DEFAULT '{}',
    notification_settings JSONB DEFAULT '{
        "email_notifications": true,
        "sms_notifications": false,
        "order_confirmations": true,
        "pickup_reminders": true,
        "delivery_notifications": true,
        "promotional_emails": false
    }',
    payment_methods TEXT[] DEFAULT ARRAY['Credit Card', 'Debit Card', 'Cash'],
    special_instructions TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id) -- One store detail record per user
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_store_details_user_id ON store_details(user_id);
CREATE INDEX IF NOT EXISTS idx_clients_user_id ON clients(user_id);

-- Create triggers for updated_at timestamps
CREATE TRIGGER trigger_store_details_updated_at
    BEFORE UPDATE ON store_details
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_clients_updated_at
    BEFORE UPDATE ON clients
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE store_details ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for clients (missing from original migration)
CREATE POLICY "Users can view their own clients" ON clients
    FOR SELECT USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can insert their own clients" ON clients
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Users can update their own clients" ON clients
    FOR UPDATE USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can delete their own clients" ON clients
    FOR DELETE USING ((select auth.uid()) = user_id);

-- Create RLS policies for store_details
CREATE POLICY "Users can view their own store_details" ON store_details
    FOR SELECT USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can insert their own store_details" ON store_details
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Users can update their own store_details" ON store_details
    FOR UPDATE USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can delete their own store_details" ON store_details
    FOR DELETE USING ((select auth.uid()) = user_id);

-- Insert default store details for existing users (optional)
-- This will create a default store detail record for users who don't have one
INSERT INTO store_details (user_id, store_name, address, city, state, zip_code, phone_number)
SELECT 
    id,
    'My Laundry Shop',
    '123 Main Street',
    'Your City',
    'Your State',
    '12345',
    '(*************'
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM store_details WHERE user_id IS NOT NULL)
ON CONFLICT (user_id) DO NOTHING;
