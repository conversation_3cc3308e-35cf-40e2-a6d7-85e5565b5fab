import 'package:flutter/material.dart';
import '../models/laundry_order.dart';
import '../utils/snackbar_helper.dart';
import '../services/laundry_order_service.dart';

class TimeSlotSelectionScreen extends StatefulWidget {
  final LaundryOrder order;

  const TimeSlotSelectionScreen({
    super.key,
    required this.order,
  });

  @override
  State<TimeSlotSelectionScreen> createState() => _TimeSlotSelectionScreenState();
}

class _TimeSlotSelectionScreenState extends State<TimeSlotSelectionScreen> {
  TimeSlot? _selectedPickupSlot;
  TimeSlot? _selectedDeliverySlot;
  
  late List<TimeSlot> _pickupSlots;
  late List<TimeSlot> _deliverySlots;

  @override
  void initState() {
    super.initState();
    _generateTimeSlots();
  }

  void _generateTimeSlots() {
    final now = DateTime.now();
    _pickupSlots = [];
    _deliverySlots = [];

    // Generate pickup slots for next 7 days
    for (int i = 1; i <= 7; i++) {
      final date = now.add(Duration(days: i));
      
      // Skip Sundays for pickup
      if (date.weekday != 7) {
        _pickupSlots.addAll([
          TimeSlot(date: date, timeRange: '8:00 AM - 10:00 AM'),
          TimeSlot(date: date, timeRange: '10:00 AM - 12:00 PM'),
          TimeSlot(date: date, timeRange: '12:00 PM - 2:00 PM'),
          TimeSlot(date: date, timeRange: '2:00 PM - 4:00 PM'),
          TimeSlot(date: date, timeRange: '4:00 PM - 6:00 PM'),
        ]);
      }
    }

    // Generate delivery slots (2-3 days after pickup depending on service)
    final deliveryStartDay = widget.order.serviceTypes.contains(ServiceType.ironing) ? 2 : 3;
    for (int i = deliveryStartDay; i <= deliveryStartDay + 7; i++) {
      final date = now.add(Duration(days: i));
      
      // Skip Sundays for delivery
      if (date.weekday != 7) {
        _deliverySlots.addAll([
          TimeSlot(date: date, timeRange: '8:00 AM - 10:00 AM'),
          TimeSlot(date: date, timeRange: '10:00 AM - 12:00 PM'),
          TimeSlot(date: date, timeRange: '12:00 PM - 2:00 PM'),
          TimeSlot(date: date, timeRange: '2:00 PM - 4:00 PM'),
          TimeSlot(date: date, timeRange: '4:00 PM - 6:00 PM'),
        ]);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Time Slots'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            child: Column(
              children: [
                Icon(
                  Icons.schedule,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 12),
                Text(
                  'Choose Time Slots',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Select when we should pick up and deliver your laundry',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          Expanded(
            child: DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  TabBar(
                    labelColor: Theme.of(context).colorScheme.primary,
                    unselectedLabelColor: Colors.grey[600],
                    indicatorColor: Theme.of(context).colorScheme.primary,
                    tabs: const [
                      Tab(
                        icon: Icon(Icons.upload),
                        text: 'Pickup',
                      ),
                      Tab(
                        icon: Icon(Icons.download),
                        text: 'Delivery',
                      ),
                    ],
                  ),
                  
                  Expanded(
                    child: TabBarView(
                      children: [
                        // Pickup Tab
                        _buildTimeSlotList(
                          slots: _pickupSlots,
                          selectedSlot: _selectedPickupSlot,
                          onSlotSelected: (slot) {
                            setState(() {
                              _selectedPickupSlot = slot;
                            });
                          },
                          emptyMessage: 'No pickup slots available',
                        ),
                        
                        // Delivery Tab
                        _buildTimeSlotList(
                          slots: _deliverySlots,
                          selectedSlot: _selectedDeliverySlot,
                          onSlotSelected: (slot) {
                            setState(() {
                              _selectedDeliverySlot = slot;
                            });
                          },
                          emptyMessage: 'No delivery slots available',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Summary and Complete Order
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_selectedPickupSlot != null || _selectedDeliverySlot != null) ...[
                  Text(
                    'Selected Time Slots',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  if (_selectedPickupSlot != null)
                    _buildSelectedSlotInfo(
                      'Pickup',
                      _selectedPickupSlot!,
                      Icons.upload,
                      Colors.blue,
                    ),
                  
                  if (_selectedDeliverySlot != null)
                    _buildSelectedSlotInfo(
                      'Delivery',
                      _selectedDeliverySlot!,
                      Icons.download,
                      Colors.green,
                    ),
                  
                  const SizedBox(height: 16),
                ],
                
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _selectedPickupSlot != null && _selectedDeliverySlot != null
                        ? _completeOrder
                        : null,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      _selectedPickupSlot != null && _selectedDeliverySlot != null
                          ? 'Complete Order'
                          : 'Select Both Time Slots',
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlotList({
    required List<TimeSlot> slots,
    required TimeSlot? selectedSlot,
    required Function(TimeSlot) onSlotSelected,
    required String emptyMessage,
  }) {
    if (slots.isEmpty) {
      return Center(
        child: Text(
          emptyMessage,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      );
    }

    // Group slots by date
    final groupedSlots = <DateTime, List<TimeSlot>>{};
    for (final slot in slots) {
      final dateKey = DateTime(slot.date.year, slot.date.month, slot.date.day);
      groupedSlots.putIfAbsent(dateKey, () => []).add(slot);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedSlots.length,
      itemBuilder: (context, index) {
        final date = groupedSlots.keys.elementAt(index);
        final daySlots = groupedSlots[date]!;

        return Container(
          margin: const EdgeInsets.only(bottom: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                daySlots.first.formattedDate,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: daySlots.map((slot) {
                  final isSelected = selectedSlot == slot;
                  return InkWell(
                    onTap: () => onSlotSelected(slot),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey[300]!,
                        ),
                      ),
                      child: Text(
                        slot.timeRange,
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.grey[700],
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSelectedSlotInfo(String type, TimeSlot slot, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Text(
            '$type: ',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text('${slot.formattedDate} ${slot.timeRange}'),
        ],
      ),
    );
  }

  void _completeOrder() async {
    if (_selectedPickupSlot == null || _selectedDeliverySlot == null) {
      SnackbarHelper.showError(
        context,
        'Please select both pickup and delivery time slots.',
      );
      return;
    }

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Update the order with selected time slots
      widget.order.pickupTimeSlot = _selectedPickupSlot;
      widget.order.deliveryTimeSlot = _selectedDeliverySlot;

      // Save the order to Supabase
      final orderNumber = await LaundryOrderService.saveLaundryOrder(widget.order);

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      // Show success message
      SnackbarHelper.showSuccess(
        context,
        'Order #$orderNumber created successfully! We\'ll pick up your laundry on ${_selectedPickupSlot!.formattedDate}.',
      );

      // Navigate back to home screen
      if (mounted) {
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    } catch (e) {
      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      // Show error message
      SnackbarHelper.showError(
        context,
        'Failed to create order: ${e.toString()}',
      );
    }
  }
}
