import 'package:supabase_flutter/supabase_flutter.dart';
import 'session_manager.dart';

class AuthService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final SessionManager _sessionManager = SessionManager.instance;

  // Sign in with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user == null) {
        throw Exception('Sign in failed. Please check your credentials.');
      }
      
      return response;
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('An unexpected error occurred. Please try again.');
    }
  }

  // Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
        },
      );
      
      return response;
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('An unexpected error occurred. Please try again.');
    }
  }

  // Reset password
  Future<void> resetPassword({required String email}) async {
    try {
      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: 'io.supabase.dropngo://reset-password',
      );
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('An unexpected error occurred. Please try again.');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _sessionManager.signOut();
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('An unexpected error occurred while signing out.');
    }
  }

  // Get current user
  User? get currentUser => _supabase.auth.currentUser;

  // Get current session
  Session? get currentSession => _supabase.auth.currentSession;

  // Listen to auth state changes
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;

  // Handle auth exceptions and provide user-friendly messages
  String _handleAuthException(AuthException e) {
    switch (e.message.toLowerCase()) {
      case 'invalid login credentials':
        return 'Invalid email or password. Please check your credentials and try again.';
      case 'email not confirmed':
        return 'Please check your email and click the confirmation link before signing in.';
      case 'user not found':
        return 'No account found with this email address.';
      case 'invalid email':
        return 'Please enter a valid email address.';
      case 'weak password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'email already registered':
      case 'user already registered':
        return 'An account with this email already exists. Please sign in instead.';
      case 'signup disabled':
        return 'Account registration is currently disabled. Please contact support.';
      case 'too many requests':
        return 'Too many attempts. Please wait a moment before trying again.';
      case 'network error':
        return 'Network error. Please check your internet connection and try again.';
      default:
        // Return the original message if we don't have a specific handler
        return e.message.isNotEmpty 
          ? e.message 
          : 'An authentication error occurred. Please try again.';
    }
  }

  // Check if user is signed in
  bool get isSignedIn => currentUser != null;

  // Check if user email is confirmed
  bool get isEmailConfirmed => currentUser?.emailConfirmedAt != null;

  // Resend confirmation email
  Future<void> resendConfirmation({required String email}) async {
    try {
      await _supabase.auth.resend(
        type: OtpType.signup,
        email: email,
      );
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Failed to resend confirmation email. Please try again.');
    }
  }

  // Update user profile
  Future<UserResponse> updateProfile({
    String? fullName,
    String? avatarUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      final updates = <String, dynamic>{};
      
      if (fullName != null) updates['full_name'] = fullName;
      if (avatarUrl != null) updates['avatar_url'] = avatarUrl;
      if (data != null) updates.addAll(data);

      final response = await _supabase.auth.updateUser(
        UserAttributes(data: updates),
      );
      
      return response;
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Failed to update profile. Please try again.');
    }
  }

  // Change password
  Future<UserResponse> changePassword({required String newPassword}) async {
    try {
      final response = await _supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      
      return response;
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Failed to change password. Please try again.');
    }
  }
}
