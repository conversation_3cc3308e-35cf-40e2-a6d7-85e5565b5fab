import 'package:flutter/material.dart';
import '../services/custom_service_management_service.dart';
import '../utils/snackbar_helper.dart';
import 'add_edit_service_screen.dart';

class CustomServicesScreen extends StatefulWidget {
  const CustomServicesScreen({super.key});

  @override
  State<CustomServicesScreen> createState() => _CustomServicesScreenState();
}

class _CustomServicesScreenState extends State<CustomServicesScreen> {
  List<Map<String, dynamic>> _services = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadServices();
  }

  Future<void> _loadServices() async {
    try {
      final services = await CustomServiceManagementService.getUserCustomServices();
      if (mounted) {
        setState(() {
          _services = services;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        SnackbarHelper.showError(
          context,
          'Failed to load services: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _deleteService(String serviceId, String serviceName) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Service'),
        content: Text('Are you sure you want to delete "$serviceName"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await CustomServiceManagementService.deleteCustomService(serviceId);
        if (mounted) {
          SnackbarHelper.showSuccess(context, 'Service deleted successfully');
          _loadServices(); // Reload the list
        }
      } catch (e) {
        if (mounted) {
          SnackbarHelper.showError(context, 'Failed to delete service: ${e.toString()}');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Custom Services'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddEditServiceScreen(),
                ),
              );
              if (result == true) {
                _loadServices(); // Reload if service was added
              }
            },
            icon: const Icon(Icons.add),
            tooltip: 'Add Service',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _services.isEmpty
              ? _buildEmptyState()
              : _buildServicesList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddEditServiceScreen(),
            ),
          );
          if (result == true) {
            _loadServices(); // Reload if service was added
          }
        },
        tooltip: 'Add Service',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cleaning_services_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Custom Services',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first custom service to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddEditServiceScreen(),
                  ),
                );
                if (result == true) {
                  _loadServices();
                }
              },
              icon: const Icon(Icons.add),
              label: const Text('Add Service'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServicesList() {
    // Group services by type
    final groupedServices = <String, List<Map<String, dynamic>>>{};
    for (final service in _services) {
      final type = service['service_type'] as String;
      groupedServices.putIfAbsent(type, () => []).add(service);
    }

    return RefreshIndicator(
      onRefresh: _loadServices,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: groupedServices.length,
        itemBuilder: (context, index) {
          final serviceType = groupedServices.keys.elementAt(index);
          final services = groupedServices[serviceType]!;
          
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Service Type Header
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Text(
                  _getServiceTypeDisplayName(serviceType),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              
              // Services in this type
              ...services.map((service) => _buildServiceCard(service)),
              
              const SizedBox(height: 16),
            ],
          );
        },
      ),
    );
  }

  Widget _buildServiceCard(Map<String, dynamic> service) {
    final title = service['title'] as String;
    final description = service['description'] as String? ?? '';
    final basePrice = (service['base_price'] as num).toDouble();
    final pricingType = service['pricing_type'] as String;
    final features = List<String>.from(service['features'] as List? ?? []);
    final colorHex = service['color_hex'] as String? ?? '#2196F3';
    final serviceId = service['id'] as String;

    final color = Color(int.parse(colorHex.replaceFirst('#', '0xFF')));

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.cleaning_services,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (description.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          description,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AddEditServiceScreen(serviceId: serviceId),
                        ),
                      ).then((result) {
                        if (result == true) {
                          _loadServices();
                        }
                      });
                    } else if (value == 'delete') {
                      _deleteService(serviceId, title);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Text(
              'Starting at \$${basePrice.toStringAsFixed(2)}/${pricingType == 'per_piece' ? 'item' : 'kg'}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            
            if (features.isNotEmpty) ...[
              const SizedBox(height: 8),
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children: features.map((feature) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      feature,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[700],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getServiceTypeDisplayName(String serviceType) {
    switch (serviceType) {
      case 'wash_and_fold':
        return 'Wash & Fold Services';
      case 'dry_clean':
        return 'Dry Clean Services';
      case 'ironing':
        return 'Ironing Services';
      default:
        return serviceType.toUpperCase();
    }
  }
}
