import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/custom_service_management_service.dart';
import '../models/laundry_order.dart';
import '../utils/snackbar_helper.dart';

class AddEditGarmentScreen extends StatefulWidget {
  final String? garmentId; // null for add, non-null for edit

  const AddEditGarmentScreen({super.key, this.garmentId});

  @override
  State<AddEditGarmentScreen> createState() => _AddEditGarmentScreenState();
}

class _AddEditGarmentScreenState extends State<AddEditGarmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();

  ServiceType _selectedServiceType = ServiceType.washAndFold;
  GarmentType _selectedGarmentType = GarmentType.shirts;
  PricingType _selectedPricingType = PricingType.perPiece;
  bool _isLoading = false;
  bool _isLoadingData = false;

  @override
  void initState() {
    super.initState();
    if (widget.garmentId != null) {
      _loadGarmentData();
    }
  }

  Future<void> _loadGarmentData() async {
    setState(() {
      _isLoadingData = true;
    });

    try {
      final garment = await CustomServiceManagementService.getCustomGarmentById(widget.garmentId!);
      if (garment != null && mounted) {
        setState(() {
          _nameController.text = garment['name'] as String;
          _descriptionController.text = garment['description'] as String? ?? '';
          _priceController.text = (garment['price'] as num).toString();
          _selectedServiceType = _stringToServiceType(garment['service_type'] as String);
          _selectedGarmentType = _stringToGarmentType(garment['garment_type'] as String);
          _selectedPricingType = _stringToPricingType(garment['pricing_type'] as String);
        });
      }
    } catch (e) {
      if (mounted) {
        SnackbarHelper.showError(context, 'Failed to load garment data: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingData = false;
        });
      }
    }
  }

  ServiceType _stringToServiceType(String str) {
    switch (str) {
      case 'wash_and_fold':
        return ServiceType.washAndFold;
      case 'dry_clean':
        return ServiceType.dryClean;
      case 'ironing':
        return ServiceType.ironing;
      default:
        return ServiceType.washAndFold;
    }
  }

  GarmentType _stringToGarmentType(String str) {
    switch (str) {
      case 'shirts':
        return GarmentType.shirts;
      case 'pants':
        return GarmentType.pants;
      case 'dresses':
        return GarmentType.dresses;
      case 'suits':
        return GarmentType.suits;
      case 'jackets':
        return GarmentType.jackets;
      case 'underwear':
        return GarmentType.underwear;
      case 'socks':
        return GarmentType.socks;
      case 'bedding':
        return GarmentType.bedding;
      case 'towels':
        return GarmentType.towels;
      case 'other':
        return GarmentType.other;
      default:
        return GarmentType.other;
    }
  }

  PricingType _stringToPricingType(String str) {
    switch (str) {
      case 'per_piece':
        return PricingType.perPiece;
      case 'per_kg':
        return PricingType.perKg;
      default:
        return PricingType.perPiece;
    }
  }

  Future<void> _saveGarment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final price = double.parse(_priceController.text);

      if (widget.garmentId == null) {
        // Create new garment
        await CustomServiceManagementService.createCustomGarment(
          serviceType: _selectedServiceType,
          garmentType: _selectedGarmentType,
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          price: price,
          pricingType: _selectedPricingType,
        );
        SnackbarHelper.showSuccess(context, 'Garment created successfully');
      } else {
        // Update existing garment
        await CustomServiceManagementService.updateCustomGarment(
          garmentId: widget.garmentId!,
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          price: price,
          pricingType: _selectedPricingType,
        );
        SnackbarHelper.showSuccess(context, 'Garment updated successfully');
      }

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      SnackbarHelper.showError(context, 'Failed to save garment: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.garmentId == null ? 'Add Garment' : 'Edit Garment'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (!_isLoading && !_isLoadingData)
            TextButton(
              onPressed: _saveGarment,
              child: const Text('Save'),
            ),
        ],
      ),
      body: _isLoadingData
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Service Type Selection
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Service Type',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            DropdownButtonFormField<ServiceType>(
                              value: _selectedServiceType,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              ),
                              items: ServiceType.values.map((type) {
                                return DropdownMenuItem(
                                  value: type,
                                  child: Text(_getServiceTypeDisplayName(type)),
                                );
                              }).toList(),
                              onChanged: widget.garmentId == null ? (value) {
                                if (value != null) {
                                  setState(() {
                                    _selectedServiceType = value;
                                  });
                                }
                              } : null, // Disable editing service type for existing garments
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Garment Type Selection
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Garment Type',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            DropdownButtonFormField<GarmentType>(
                              value: _selectedGarmentType,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              ),
                              items: GarmentType.values.map((type) {
                                return DropdownMenuItem(
                                  value: type,
                                  child: Text(_getGarmentTypeDisplayName(type)),
                                );
                              }).toList(),
                              onChanged: widget.garmentId == null ? (value) {
                                if (value != null) {
                                  setState(() {
                                    _selectedGarmentType = value;
                                  });
                                }
                              } : null, // Disable editing garment type for existing garments
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Basic Information
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Garment Details',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            TextFormField(
                              controller: _nameController,
                              decoration: const InputDecoration(
                                labelText: 'Garment Name',
                                border: OutlineInputBorder(),
                                hintText: 'e.g., Premium Cotton Shirts',
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Please enter a garment name';
                                }
                                return null;
                              },
                            ),
                            
                            const SizedBox(height: 16),
                            
                            TextFormField(
                              controller: _descriptionController,
                              decoration: const InputDecoration(
                                labelText: 'Description',
                                border: OutlineInputBorder(),
                                hintText: 'Optional description of the garment',
                              ),
                              maxLines: 3,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Pricing Information
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Pricing',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: _priceController,
                                    decoration: const InputDecoration(
                                      labelText: 'Price',
                                      border: OutlineInputBorder(),
                                      prefixText: '\$',
                                    ),
                                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                                    ],
                                    validator: (value) {
                                      if (value == null || value.trim().isEmpty) {
                                        return 'Please enter a price';
                                      }
                                      final price = double.tryParse(value);
                                      if (price == null || price <= 0) {
                                        return 'Please enter a valid price';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: DropdownButtonFormField<PricingType>(
                                    value: _selectedPricingType,
                                    decoration: const InputDecoration(
                                      labelText: 'Pricing Type',
                                      border: OutlineInputBorder(),
                                    ),
                                    items: PricingType.values.map((type) {
                                      return DropdownMenuItem(
                                        value: type,
                                        child: Text(type == PricingType.perPiece ? 'Per Piece' : 'Per Kg'),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _selectedPricingType = value;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Save Button
                    ElevatedButton(
                      onPressed: _isLoading ? null : _saveGarment,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Text(widget.garmentId == null ? 'Create Garment' : 'Update Garment'),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  String _getServiceTypeDisplayName(ServiceType type) {
    switch (type) {
      case ServiceType.washAndFold:
        return 'Wash & Fold';
      case ServiceType.dryClean:
        return 'Dry Clean';
      case ServiceType.ironing:
        return 'Ironing';
    }
  }

  String _getGarmentTypeDisplayName(GarmentType type) {
    switch (type) {
      case GarmentType.shirts:
        return 'Shirts';
      case GarmentType.pants:
        return 'Pants';
      case GarmentType.dresses:
        return 'Dresses';
      case GarmentType.suits:
        return 'Suits';
      case GarmentType.jackets:
        return 'Jackets';
      case GarmentType.underwear:
        return 'Underwear';
      case GarmentType.socks:
        return 'Socks';
      case GarmentType.bedding:
        return 'Bedding';
      case GarmentType.towels:
        return 'Towels';
      case GarmentType.other:
        return 'Other';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }
}
