import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_screen.dart';
import 'home_screen.dart';
import '../utils/session_manager.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final SupabaseClient supabase = Supabase.instance.client;
  final SessionManager sessionManager = SessionManager.instance;
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();
    _initializeApp();

    // Listen to auth state changes
    supabase.auth.onAuthStateChange.listen((data) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  Future<void> _initializeApp() async {
    await sessionManager.initializeSession();
    if (mounted) {
      setState(() {
        _isInitializing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isInitializing) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final session = supabase.auth.currentSession;

    if (session != null) {
      return const HomeScreen();
    } else {
      return const LoginScreen();
    }
  }
}
