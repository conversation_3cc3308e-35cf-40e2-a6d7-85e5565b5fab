import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/store_details.dart';

class StoreDetailsService {
  static final SupabaseClient _supabase = Supabase.instance.client;

  /// Get store details for the current user
  static Future<StoreDetails?> getStoreDetails() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('store_details')
          .select('*')
          .eq('user_id', user.id)
          .maybeSingle();

      if (response == null) {
        return null;
      }

      return StoreDetails.fromJson(response);
    } catch (e) {
      throw Exception('Failed to fetch store details: ${e.toString()}');
    }
  }

  /// Create store details for the current user
  static Future<StoreDetails> createStoreDetails(StoreDetails storeDetails) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final data = storeDetails.toJson();
      data['user_id'] = user.id;
      data.remove('id'); // Remove id for insert
      data.remove('created_at'); // Let database handle timestamps
      data.remove('updated_at');

      final response = await _supabase
          .from('store_details')
          .insert(data)
          .select()
          .single();

      return StoreDetails.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create store details: ${e.toString()}');
    }
  }

  /// Update store details for the current user
  static Future<StoreDetails> updateStoreDetails(StoreDetails storeDetails) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final data = storeDetails.toJson();
      data.remove('id'); // Don't update id
      data.remove('user_id'); // Don't update user_id
      data.remove('created_at'); // Don't update created_at
      data.remove('updated_at'); // Let database handle updated_at

      final response = await _supabase
          .from('store_details')
          .update(data)
          .eq('user_id', user.id)
          .select()
          .single();

      return StoreDetails.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update store details: ${e.toString()}');
    }
  }

  /// Get or create store details for the current user
  static Future<StoreDetails> getOrCreateStoreDetails() async {
    try {
      // Try to get existing store details
      final existing = await getStoreDetails();
      if (existing != null) {
        return existing;
      }

      // Create default store details if none exist
      final defaultDetails = StoreDetails.createDefault();
      return await createStoreDetails(defaultDetails);
    } catch (e) {
      throw Exception('Failed to get or create store details: ${e.toString()}');
    }
  }

  /// Update specific fields of store details
  static Future<StoreDetails> updateStoreDetailsFields(Map<String, dynamic> updates) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('store_details')
          .update(updates)
          .eq('user_id', user.id)
          .select()
          .single();

      return StoreDetails.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update store details fields: ${e.toString()}');
    }
  }

  /// Update business hours
  static Future<StoreDetails> updateBusinessHours(Map<String, dynamic> businessHours) async {
    return await updateStoreDetailsFields({'business_hours': businessHours});
  }

  /// Update notification settings
  static Future<StoreDetails> updateNotificationSettings(Map<String, dynamic> notificationSettings) async {
    return await updateStoreDetailsFields({'notification_settings': notificationSettings});
  }

  /// Update social media links
  static Future<StoreDetails> updateSocialMedia(Map<String, dynamic> socialMedia) async {
    return await updateStoreDetailsFields({'social_media': socialMedia});
  }

  /// Update services offered
  static Future<StoreDetails> updateServicesOffered(List<String> servicesOffered) async {
    return await updateStoreDetailsFields({'services_offered': servicesOffered});
  }

  /// Update payment methods
  static Future<StoreDetails> updatePaymentMethods(List<String> paymentMethods) async {
    return await updateStoreDetailsFields({'payment_methods': paymentMethods});
  }

  /// Delete store details for the current user
  static Future<void> deleteStoreDetails() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _supabase
          .from('store_details')
          .delete()
          .eq('user_id', user.id);
    } catch (e) {
      throw Exception('Failed to delete store details: ${e.toString()}');
    }
  }

  /// Check if store details exist for the current user
  static Future<bool> storeDetailsExist() async {
    try {
      final storeDetails = await getStoreDetails();
      return storeDetails != null;
    } catch (e) {
      return false;
    }
  }

  /// Validate store details data
  static String? validateStoreDetails(StoreDetails storeDetails) {
    if (storeDetails.storeName.trim().isEmpty) {
      return 'Store name is required';
    }
    if (storeDetails.address.trim().isEmpty) {
      return 'Address is required';
    }
    if (storeDetails.city.trim().isEmpty) {
      return 'City is required';
    }
    if (storeDetails.state.trim().isEmpty) {
      return 'State is required';
    }
    if (storeDetails.zipCode.trim().isEmpty) {
      return 'ZIP code is required';
    }
    if (storeDetails.phoneNumber.trim().isEmpty) {
      return 'Phone number is required';
    }
    if (storeDetails.email != null && storeDetails.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(storeDetails.email!)) {
        return 'Invalid email format';
      }
    }
    if (storeDetails.minimumOrderAmount < 0) {
      return 'Minimum order amount cannot be negative';
    }
    if (storeDetails.deliveryFee < 0) {
      return 'Delivery fee cannot be negative';
    }
    if (storeDetails.freeDeliveryThreshold < 0) {
      return 'Free delivery threshold cannot be negative';
    }
    if (storeDetails.taxRate < 0 || storeDetails.taxRate > 1) {
      return 'Tax rate must be between 0 and 1';
    }
    if (storeDetails.pickupRadius <= 0) {
      return 'Pickup radius must be greater than 0';
    }
    return null;
  }
}
