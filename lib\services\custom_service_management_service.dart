import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/laundry_order.dart';

class CustomServiceManagementService {
  static final SupabaseClient _supabase = Supabase.instance.client;

  // Convert enum to string for database storage
  static String _serviceTypeToString(ServiceType type) {
    switch (type) {
      case ServiceType.washAndFold:
        return 'wash_and_fold';
      case ServiceType.dryClean:
        return 'dry_clean';
      case ServiceType.ironing:
        return 'ironing';
    }
  }

  static String _garmentTypeToString(GarmentType type) {
    switch (type) {
      case GarmentType.shirts:
        return 'shirts';
      case GarmentType.pants:
        return 'pants';
      case GarmentType.dresses:
        return 'dresses';
      case GarmentType.suits:
        return 'suits';
      case GarmentType.jackets:
        return 'jackets';
      case GarmentType.underwear:
        return 'underwear';
      case GarmentType.socks:
        return 'socks';
      case GarmentType.bedding:
        return 'bedding';
      case GarmentType.towels:
        return 'towels';
      case GarmentType.other:
        return 'other';
    }
  }

  static String _pricingTypeToString(PricingType type) {
    switch (type) {
      case PricingType.perPiece:
        return 'per_piece';
      case PricingType.perKg:
        return 'per_kg';
    }
  }

  // Convert string back to enum
  static ServiceType _stringToServiceType(String str) {
    switch (str) {
      case 'wash_and_fold':
        return ServiceType.washAndFold;
      case 'dry_clean':
        return ServiceType.dryClean;
      case 'ironing':
        return ServiceType.ironing;
      default:
        return ServiceType.washAndFold;
    }
  }

  static GarmentType _stringToGarmentType(String str) {
    switch (str) {
      case 'shirts':
        return GarmentType.shirts;
      case 'pants':
        return GarmentType.pants;
      case 'dresses':
        return GarmentType.dresses;
      case 'suits':
        return GarmentType.suits;
      case 'jackets':
        return GarmentType.jackets;
      case 'underwear':
        return GarmentType.underwear;
      case 'socks':
        return GarmentType.socks;
      case 'bedding':
        return GarmentType.bedding;
      case 'towels':
        return GarmentType.towels;
      case 'other':
        return GarmentType.other;
      default:
        return GarmentType.other;
    }
  }

  static PricingType _stringToPricingType(String str) {
    switch (str) {
      case 'per_piece':
        return PricingType.perPiece;
      case 'per_kg':
        return PricingType.perKg;
      default:
        return PricingType.perPiece;
    }
  }

  // ===== CUSTOM SERVICES MANAGEMENT =====

  // Get all custom services for the current user
  static Future<List<Map<String, dynamic>>> getUserCustomServices() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('custom_services')
          .select('*')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .order('service_type, title');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch custom services: ${e.toString()}');
    }
  }

  // Get custom services by service type
  static Future<List<Map<String, dynamic>>> getCustomServicesByType(ServiceType serviceType) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('custom_services')
          .select('*')
          .eq('user_id', user.id)
          .eq('service_type', _serviceTypeToString(serviceType))
          .eq('is_active', true)
          .order('title');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch custom services: ${e.toString()}');
    }
  }

  // Create a new custom service
  static Future<String> createCustomService({
    required ServiceType serviceType,
    required String title,
    required String description,
    required double basePrice,
    required PricingType pricingType,
    required List<String> features,
    String iconName = 'cleaning_services',
    String colorHex = '#2196F3',
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase.from('custom_services').insert({
        'user_id': user.id,
        'service_type': _serviceTypeToString(serviceType),
        'title': title,
        'description': description,
        'base_price': basePrice,
        'pricing_type': _pricingTypeToString(pricingType),
        'features': features,
        'icon_name': iconName,
        'color_hex': colorHex,
        'is_active': true,
      }).select('id').single();

      return response['id'] as String;
    } catch (e) {
      throw Exception('Failed to create custom service: ${e.toString()}');
    }
  }

  // Update an existing custom service
  static Future<void> updateCustomService({
    required String serviceId,
    required String title,
    required String description,
    required double basePrice,
    required PricingType pricingType,
    required List<String> features,
    String iconName = 'cleaning_services',
    String colorHex = '#2196F3',
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _supabase.from('custom_services').update({
        'title': title,
        'description': description,
        'base_price': basePrice,
        'pricing_type': _pricingTypeToString(pricingType),
        'features': features,
        'icon_name': iconName,
        'color_hex': colorHex,
      }).eq('id', serviceId).eq('user_id', user.id);
    } catch (e) {
      throw Exception('Failed to update custom service: ${e.toString()}');
    }
  }

  // Delete a custom service (soft delete)
  static Future<void> deleteCustomService(String serviceId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _supabase
          .from('custom_services')
          .update({'is_active': false})
          .eq('id', serviceId)
          .eq('user_id', user.id);
    } catch (e) {
      throw Exception('Failed to delete custom service: ${e.toString()}');
    }
  }

  // ===== CUSTOM GARMENTS MANAGEMENT =====

  // Get all custom garments for the current user
  static Future<List<Map<String, dynamic>>> getUserCustomGarments() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('custom_garments')
          .select('*')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .order('service_type, garment_type, name');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch custom garments: ${e.toString()}');
    }
  }

  // Get custom garments by service type
  static Future<List<Map<String, dynamic>>> getCustomGarmentsByServiceType(ServiceType serviceType) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('custom_garments')
          .select('*')
          .eq('user_id', user.id)
          .eq('service_type', _serviceTypeToString(serviceType))
          .eq('is_active', true)
          .order('garment_type, name');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch custom garments: ${e.toString()}');
    }
  }

  // Create a new custom garment
  static Future<String> createCustomGarment({
    required ServiceType serviceType,
    required GarmentType garmentType,
    required String name,
    required String description,
    required double price,
    required PricingType pricingType,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase.from('custom_garments').insert({
        'user_id': user.id,
        'service_type': _serviceTypeToString(serviceType),
        'garment_type': _garmentTypeToString(garmentType),
        'name': name,
        'description': description,
        'price': price,
        'pricing_type': _pricingTypeToString(pricingType),
        'is_active': true,
      }).select('id').single();

      return response['id'] as String;
    } catch (e) {
      throw Exception('Failed to create custom garment: ${e.toString()}');
    }
  }

  // Update an existing custom garment
  static Future<void> updateCustomGarment({
    required String garmentId,
    required String name,
    required String description,
    required double price,
    required PricingType pricingType,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _supabase.from('custom_garments').update({
        'name': name,
        'description': description,
        'price': price,
        'pricing_type': _pricingTypeToString(pricingType),
      }).eq('id', garmentId).eq('user_id', user.id);
    } catch (e) {
      throw Exception('Failed to update custom garment: ${e.toString()}');
    }
  }

  // Delete a custom garment (soft delete)
  static Future<void> deleteCustomGarment(String garmentId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _supabase
          .from('custom_garments')
          .update({'is_active': false})
          .eq('id', garmentId)
          .eq('user_id', user.id);
    } catch (e) {
      throw Exception('Failed to delete custom garment: ${e.toString()}');
    }
  }

  // Get a single custom service by ID
  static Future<Map<String, dynamic>?> getCustomServiceById(String serviceId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('custom_services')
          .select('*')
          .eq('id', serviceId)
          .eq('user_id', user.id)
          .maybeSingle();

      return response;
    } catch (e) {
      throw Exception('Failed to fetch custom service: ${e.toString()}');
    }
  }

  // Get a single custom garment by ID
  static Future<Map<String, dynamic>?> getCustomGarmentById(String garmentId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('custom_garments')
          .select('*')
          .eq('id', garmentId)
          .eq('user_id', user.id)
          .maybeSingle();

      return response;
    } catch (e) {
      throw Exception('Failed to fetch custom garment: ${e.toString()}');
    }
  }
}
