import 'package:flutter/material.dart';
import '../models/laundry_order.dart';
import 'address_selection_screen.dart';

class GarmentSelectionScreen extends StatefulWidget {
  final LaundryOrder order;

  const GarmentSelectionScreen({
    super.key,
    required this.order,
  });

  @override
  State<GarmentSelectionScreen> createState() => _GarmentSelectionScreenState();
}

class _GarmentSelectionScreenState extends State<GarmentSelectionScreen> {
  late List<GarmentItem> _garments;
  
  @override
  void initState() {
    super.initState();
    _garments = GarmentDefinitions.getGarmentsForServices(widget.order.serviceTypes);
  }

  @override
  Widget build(BuildContext context) {
    final totalItems = _garments.fold(0, (sum, item) => sum + item.quantity);
    final totalPrice = _garments.fold(0.0, (sum, item) => sum + item.totalPrice);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Garments'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            child: Column(
              children: [
                Icon(
                  Icons.checkroom,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 12),
                Text(
                  'Add Your Garments',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Select the items you want to include in your ${widget.order.serviceTypesDisplayName} order',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          // Garment List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _garments.length,
              itemBuilder: (context, index) {
                final garment = _garments[index];
                
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  _getGarmentIcon(garment.type),
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      garment.name,
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      garment.description,
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Text(
                                '\$${garment.price.toStringAsFixed(2)}',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 16),
                          
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Quantity',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Row(
                                children: [
                                  IconButton(
                                    onPressed: garment.quantity > 0 ? () {
                                      setState(() {
                                        garment.quantity--;
                                      });
                                    } : null,
                                    icon: const Icon(Icons.remove_circle_outline),
                                    style: IconButton.styleFrom(
                                      backgroundColor: garment.quantity > 0 
                                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                                        : Colors.grey[200],
                                    ),
                                  ),
                                  Container(
                                    width: 50,
                                    padding: const EdgeInsets.symmetric(vertical: 8),
                                    child: Text(
                                      '${garment.quantity}',
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  IconButton(
                                    onPressed: () {
                                      setState(() {
                                        garment.quantity++;
                                      });
                                    },
                                    icon: const Icon(Icons.add_circle_outline),
                                    style: IconButton.styleFrom(
                                      backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          
                          if (garment.quantity > 0) ...[
                            const SizedBox(height: 8),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'Subtotal: \$${garment.totalPrice.toStringAsFixed(2)}',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Summary and Continue
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '$totalItems items',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Total: \$${totalPrice.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: totalItems > 0 ? _continueToAddress : null,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Continue to Address',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getGarmentIcon(GarmentType type) {
    switch (type) {
      case GarmentType.shirts:
        return Icons.checkroom;
      case GarmentType.pants:
        return Icons.accessibility_new;
      case GarmentType.dresses:
        return Icons.woman;
      case GarmentType.suits:
        return Icons.business_center;
      case GarmentType.jackets:
        return Icons.checkroom;
      case GarmentType.underwear:
        return Icons.favorite;
      case GarmentType.socks:
        return Icons.sports_handball;
      case GarmentType.bedding:
        return Icons.bed;
      case GarmentType.towels:
        return Icons.dry;
      case GarmentType.other:
        return Icons.more_horiz;
    }
  }

  void _continueToAddress() {
    // Update the order with selected garments
    final selectedGarments = _garments.where((g) => g.quantity > 0 || (g.weight != null && g.weight! > 0)).toList();
    final updatedOrder = LaundryOrder(
      serviceTypes: widget.order.serviceTypes,
      garments: selectedGarments,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddressSelectionScreen(order: updatedOrder),
      ),
    );
  }
}
